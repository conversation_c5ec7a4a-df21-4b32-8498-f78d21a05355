// Chemistry content data for AQA Chemistry
const chemistryData = {
  "keyTopic1": {
    "title": "Rate and Extent of Chemical Change",
    "subtopics": [
      {
        "id": "5.6.1",
        "title": "Rate of Reaction",
        "content": [
          "Rate measures how fast reactants are used or products form: Rate = (Amount of reactant used ÷ Time) or (Amount of product formed ÷ Time). Units: g/s, cm³/s; Higher Tier: mol/s.",
          "Calculate mean rate from data (e.g., 10 g used in 20 s = 0.5 g/s).",
          "Factors: Concentration (more particles), pressure (gases closer), surface area (smaller pieces), temperature (faster particles), catalysts (speed up). Mnemonic: 'CATS' (Concentration, Area, Temperature, Surface).",
          "Collision theory: Particles must collide with enough energy (activation energy). Higher concentration, pressure, surface area increase collisions; temperature adds energy.",
          "Catalysts lower activation energy via a different pathway, not used up. Example: MnO₂ in H₂O₂ decomposition.",
          "Practical 11: Measure gas volume (e.g., CO₂ from acid + carbonate) or color change (e.g., iodine clock) to test concentration effects.",
          "Higher Tier: Draw tangents on rate graphs, calculate gradient for rate at a time."
        ]
      },
      {
        "id": "5.6.2",
        "title": "Reversible Reactions and Dynamic Equilibrium",
        "content": [
          "Reversible reactions: Products can reform reactants (e.g., A + B ⇌ C + D). Example: Hydrated copper sulfate (blue) ⇌ anhydrous (white) + water.",
          "Energy: Exothermic one way (releases heat), endothermic the other (absorbs heat). Same energy transferred.",
          "Equilibrium: In closed systems, forward/reverse reactions occur at same rate.",
          "Higher Tier: Le Chatelier's Principle—system shifts to counteract changes. Mnemonic: 'EPC' (Equilibrium shifts: Pressure, Concentration, Temperature).",
          "Concentration: Increase reactant = more products; decrease product = more reactants.",
          "Temperature: Increase favors endothermic; decrease favors exothermic (e.g., N₂ + 3H₂ ⇌ 2NH₃, exothermic, less NH₃ if hotter).",
          "Pressure (gases): Increase shifts to fewer molecules; decrease to more (e.g., N₂ + 3H₂ ⇌ 2NH₃, 4 molecules left, 2 right, high pressure favors right)."
        ]
      }
    ]
  },
  "keyTopic2": {
    "title": "Organic Chemistry",
    "subtopics": [
      {
        "id": "5.7.1",
        "title": "Carbon Compounds as Fuels and Feedstock",
        "content": [
          "Crude oil: Finite resource from ancient plankton, mixture of hydrocarbons (carbon + hydrogen).",
          "Alkanes: Hydrocarbons with formula CₙH₂ₙ₊₂. First four: Methane (CH₄), ethane (C₂H₆), propane (C₃H₈), butane (C₄H₁₀).",
          "Fractional distillation: Separates crude oil by boiling points. Heat oil (evaporates), cool in column (condenses). Fractions: Petrol, diesel, kerosene, lubricants, polymers, detergents. Mnemonic: 'FCP' (Fractional distillation, Combustion, Cracking).",
          "Properties: Longer chains = higher boiling point, more viscous (thick), less flammable.",
          "Combustion: Hydrocarbon + O₂ → CO₂ + H₂O (complete). Example: C₃H₈ + 5O₂ → 3CO₂ + 4H₂O.",
          "Cracking: Breaks long hydrocarbons into smaller ones (alkanes, alkenes). Methods: Catalytic (heat + catalyst), steam (high temp + steam).",
          "Alkenes: More reactive, turn bromine water colorless (test). Used for fuels, polymers, chemicals."
        ]
      }
    ]
  },
  "keyTopic3": {
    "title": "Chemical Analysis",
    "subtopics": [
      {
        "id": "5.8.1",
        "title": "Purity, Formulations, and Chromatography",
        "content": [
          "Pure substance: Single element/compound, fixed melting/boiling points (mixtures vary).",
          "Formulations: Designed mixtures (e.g., medicines, paints, fuels) with specific proportions.",
          "Chromatography: Separates mixtures using stationary phase (paper) and mobile phase (solvent). Practical 12: Separate colored substances, calculate Rf.",
          "Rf value: Rf = (Distance by substance) ÷ (Distance by solvent). Example: 4 cm ÷ 10 cm = 0.4. Pure substance = one spot."
        ]
      },
      {
        "id": "5.8.2",
        "title": "Identification of Common Gases",
        "content": [
          "Hydrogen: Burning splint → pop sound.",
          "Oxygen: Glowing splint → relights.",
          "Carbon dioxide: Limewater → turns milky.",
          "Chlorine: Damp litmus → bleached white.",
          "Mnemonic: 'POCL' (Pop, Oxygen relights, Cloudy limewater, Litmus bleached)."
        ]
      }
    ]
  },
  "keyTopic4": {
    "title": "Chemistry of the Atmosphere",
    "subtopics": [
      {
        "id": "5.9.1",
        "title": "Composition and Evolution of the Atmosphere",
        "content": [
          "Current atmosphere: ~80% nitrogen, ~20% oxygen, small amounts CO₂, water vapor, noble gases.",
          "Early atmosphere: Volcanic activity gave mostly CO₂, little O₂, some nitrogen, methane, ammonia. Oceans formed, CO₂ dissolved, formed carbonates.",
          "Oxygen increase: Algae/plants via photosynthesis (6CO₂ + 6H₂O → C₆H₁₂O₆ + 6O₂), started ~2.7 billion years ago.",
          "CO₂ decrease: Photosynthesis, sedimentary rocks (limestone), fossil fuels (coal, oil)."
        ]
      },
      {
        "id": "5.9.2",
        "title": "Greenhouse Gases and Climate Change",
        "content": [
          "Greenhouse gases (CO₂, methane, water vapor) trap heat (greenhouse effect). Mnemonic: 'GAC' (Greenhouse gases, Atmosphere evolution, Climate change).",
          "Human activities: CO₂ from burning fuels, deforestation; methane from farming, landfills.",
          "Climate change: Rising temperatures, melting ice, floods, droughts.",
          "Carbon footprint: Total greenhouse gases from a product/event. Reduce via renewables, recycling."
        ]
      },
      {
        "id": "5.9.3",
        "title": "Atmospheric Pollutants",
        "content": [
          "From fuels: CO (toxic), SO₂ (acid rain), NOₓ (respiratory issues), particulates (global dimming, health problems).",
          "Example: Burning coal (C + S) produces CO₂, SO₂."
        ]
      }
    ]
  },
  "keyTopic5": {
    "title": "Using Resources",
    "subtopics": [
      {
        "id": "5.10.1",
        "title": "Using Earth's Resources and Potable Water",
        "content": [
          "Resources: Finite (oil, metals), renewable (timber, crops). Sustainable development meets current needs without harming future.",
          "Potable water: Safe to drink, contains dissolved substances. UK process: Filter fresh water, sterilize (chlorine, ozone, UV).",
          "Desalination: Distillation or reverse osmosis (energy-intensive). Practical 13: Test water pH, solids, purify by distillation.",
          "Waste water treatment: Screen debris, sediment (sludge + effluent), anaerobic digestion (sludge), aerobic treatment (effluent).",
          "Higher Tier: Metal extraction—Phytomining (plants absorb metals, burn to ash), bioleaching (bacteria produce metal solutions). Example: Copper via displacement or electrolysis."
        ]
      },
      {
        "id": "5.10.2",
        "title": "Life Cycle Assessment and Recycling",
        "content": [
          "Life cycle assessment (LCA): Assess environmental impact—raw materials, manufacturing, use, disposal. Example: Plastic vs. paper bags.",
          "Recycling: Reduces resource use (e.g., melt metals, reuse glass). Mnemonic: 'WMR' (Water, Metals, Recycling)."
        ]
      }
    ]
  },
  "keyTopic6": {
    "title": "Key Ideas in Chemistry",
    "subtopics": [
      {
        "id": "5.11",
        "title": "Fundamental Concepts",
        "content": [
          "Atoms: ~100 elements, form matter, show periodic properties due to atomic structure.",
          "Bonding: Ionic (electron transfer), covalent (electron sharing). Shapes of molecules affect behavior.",
          "Reactions: Occur via proton transfer, electron transfer, or electron sharing. Rates vary due to barriers.",
          "Energy: Conserved in reactions, neither created nor destroyed."
        ]
      }
    ]
  }
};
