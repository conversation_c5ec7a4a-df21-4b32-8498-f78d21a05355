// Quiz App JavaScript
class QuizApp {
    constructor() {
        this.currentSubject = 'history';
        this.currentTopic = null;
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.selectedAnswer = null;
        this.questionAnswered = false;
        this.autoProgressTimer = null;
        this.currentUser = null;

        this.initializeEventListeners();
        this.initializeAuth();
    }

    initializeAuth() {
        // Check if user is already logged in
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);

            // Ensure backward compatibility - add settings if they don't exist
            if (!this.currentUser.settings) {
                this.currentUser.settings = {
                    autoSkipEnabled: true
                };
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }

            // Ensure backward compatibility - add chemistry stats if they don't exist
            if (!this.currentUser.stats.hasOwnProperty('chemistryQuizzes')) {
                this.currentUser.stats.chemistryQuizzes = 0;
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }

            this.showUserInterface();
        } else {
            this.showAuthInterface();
        }
    }

    showUserInterface() {
        // Update dropdown username
        document.getElementById('dropdown-username').textContent = this.currentUser.username;
        this.showSubjectScreen();
    }

    showAuthInterface() {
        this.showSubjectScreen();
    }

    toggleUserDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        if (dropdown.classList.contains('hidden')) {
            this.showUserDropdown();
        } else {
            this.hideUserDropdown();
        }
    }

    showUserDropdown() {
        document.getElementById('user-dropdown').classList.remove('hidden');
    }

    hideUserDropdown() {
        document.getElementById('user-dropdown').classList.add('hidden');
    }

    initializeEventListeners() {
        // Subject tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const subject = e.currentTarget.dataset.subject;
                this.switchSubject(subject);
            });
        });

        // Topic selection
        document.querySelectorAll('.topic-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const topic = e.currentTarget.dataset.topic;
                this.startQuiz(topic);
            });
        });

        // Quiz controls
        document.getElementById('next-btn').addEventListener('click', () => {
            // Clear auto-progress if user manually clicks
            this.clearAutoProgress();
            this.nextQuestion();
        });

        document.getElementById('back-to-topics').addEventListener('click', () => {
            this.showSubjectScreen();
            this.resetQuiz();
        });

        // Results actions
        document.getElementById('retake-quiz').addEventListener('click', () => {
            this.startQuiz(this.currentTopic);
        });

        document.getElementById('new-topic').addEventListener('click', () => {
            this.showSubjectScreen();
            this.resetQuiz();
        });

        // Authentication event listeners
        document.getElementById('user-icon').addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.currentUser) {
                // If logged in, toggle dropdown
                this.toggleUserDropdown();
            } else {
                // If not logged in, show login modal
                this.showModal('login');
            }
        });

        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        document.getElementById('profile-btn').addEventListener('click', () => {
            this.showProfile();
        });

        document.getElementById('settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        // Modal event listeners
        document.querySelector('.close').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('show-register').addEventListener('click', (e) => {
            e.preventDefault();
            this.switchAuthForm('register');
        });

        document.getElementById('show-login').addEventListener('click', (e) => {
            e.preventDefault();
            this.switchAuthForm('login');
        });

        // Form submissions
        document.getElementById('login-form-element').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('register-form-element').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Profile actions
        document.getElementById('back-from-profile').addEventListener('click', () => {
            this.showSubjectScreen();
        });

        document.getElementById('clear-data').addEventListener('click', () => {
            this.clearUserData();
        });

        // Settings actions
        document.getElementById('back-from-settings').addEventListener('click', () => {
            this.showSubjectScreen();
        });

        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('auth-modal');
            if (e.target === modal) {
                this.hideModal();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('user-dropdown');
            const userIcon = document.getElementById('user-icon');

            if (!dropdown.contains(e.target) && !userIcon.contains(e.target)) {
                this.hideUserDropdown();
            }
        });
    }

    switchSubject(subject) {
        this.currentSubject = subject;

        // Update tab styling
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-subject="${subject}"]`).classList.add('active');

        this.showSubjectScreen();
    }

    showSubjectScreen() {
        if (this.currentSubject === 'history') {
            this.showScreen('history-topic-selection');
        } else if (this.currentSubject === 'spanish') {
            this.showScreen('spanish-topic-selection');
        } else if (this.currentSubject === 'chemistry') {
            this.showScreen('chemistry-topic-selection');
        }
    }

    showModal(type) {
        document.getElementById('auth-modal').style.display = 'block';
        if (type === 'login') {
            this.switchAuthForm('login');
        } else {
            this.switchAuthForm('register');
        }
    }

    hideModal() {
        document.getElementById('auth-modal').style.display = 'none';
        this.clearAuthForms();
    }

    switchAuthForm(type) {
        if (type === 'login') {
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('register-form').classList.add('hidden');
        } else {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('register-form').classList.remove('hidden');
        }
    }

    clearAuthForms() {
        document.getElementById('login-form-element').reset();
        document.getElementById('register-form-element').reset();
    }

    handleLogin() {
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.username === username && u.password === password);

        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            this.showUserInterface();
            this.hideModal();
            this.showMessage('Login successful!', 'success');
        } else {
            this.showMessage('Invalid username or password!', 'error');
        }
    }

    handleRegister() {
        const username = document.getElementById('register-username').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        if (password !== confirmPassword) {
            this.showMessage('Passwords do not match!', 'error');
            return;
        }

        // Get existing users
        const users = JSON.parse(localStorage.getItem('users') || '[]');

        // Check if username already exists
        if (users.find(u => u.username === username)) {
            this.showMessage('Username already exists!', 'error');
            return;
        }

        // Create new user
        const newUser = {
            username,
            email,
            password,
            joinDate: new Date().toISOString(),
            settings: {
                autoSkipEnabled: true
            },
            stats: {
                totalQuizzes: 0,
                totalQuestions: 0,
                totalCorrect: 0,
                historyQuizzes: 0,
                spanishQuizzes: 0,
                chemistryQuizzes: 0,
                bestScore: 0,
                quizHistory: []
            }
        };

        users.push(newUser);
        localStorage.setItem('users', JSON.stringify(users));

        this.currentUser = newUser;
        localStorage.setItem('currentUser', JSON.stringify(newUser));

        this.showUserInterface();
        this.hideModal();
        this.showMessage('Registration successful!', 'success');
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        this.hideUserDropdown();
        this.showAuthInterface();
        this.showMessage('Logged out successfully!', 'success');
    }

    showMessage(message, type) {
        // Create a temporary message element
        const messageEl = document.createElement('div');
        messageEl.className = `notification ${type}`;

        // Add icon based on type
        const icon = document.createElement('span');
        icon.innerHTML = this.getNotificationIcon(type);

        // Add message text
        const messageText = document.createElement('span');
        messageText.textContent = message;

        messageEl.appendChild(icon);
        messageEl.appendChild(messageText);

        document.body.appendChild(messageEl);

        // Auto-remove after 3 seconds with fade-out animation
        setTimeout(() => {
            messageEl.classList.add('fade-out');
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 300); // Wait for fade-out animation to complete
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>',
            error: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>',
            warning: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>'
        };
        return icons[type] || icons.success;
    }

    startQuiz(topic) {
        this.currentTopic = topic;

        if (this.currentSubject === 'history') {
            this.questions = this.generateHistoryQuestions(topic);
        } else if (this.currentSubject === 'spanish') {
            this.questions = this.generateSpanishQuestions(topic);
        } else if (this.currentSubject === 'chemistry') {
            this.questions = this.generateChemistryQuestions(topic);
        }

        this.currentQuestionIndex = 0;
        this.score = 0;
        this.showScreen('quiz-screen');
        this.displayQuestion();
    }

    generateHistoryQuestions(topic) {
        // Get all available predefined questions
        const allTrueFalseQuestions = this.getTrueFalseStatements();
        const allMultipleChoiceQuestions = this.getMultipleChoiceQuestions();

        let availableQuestions = [];

        if (topic === 'all') {
            // Use all questions
            availableQuestions = [...allTrueFalseQuestions, ...allMultipleChoiceQuestions];
        } else {
            // Filter questions for specific topic
            const topicData = contentData[topic];
            availableQuestions = [
                ...allTrueFalseQuestions.filter(q => q.topic === topicData.title),
                ...allMultipleChoiceQuestions.filter(q => q.topic === topicData.title)
            ];
        }

        // Shuffle and select 15 unique questions
        const shuffledQuestions = this.shuffleArray(availableQuestions);
        const selectedQuestions = shuffledQuestions.slice(0, Math.min(15, shuffledQuestions.length));

        // Convert to quiz format
        const questions = selectedQuestions.map(q => {
            if (q.type === 'true-false') {
                return {
                    type: 'true-false',
                    question: `True or False: ${q.statement}`,
                    answers: ['True', 'False'],
                    correctAnswer: q.isTrue ? 'True' : 'False',
                    subtopic: q.subtopic,
                    id: q.id // Add unique ID to prevent duplicates
                };
            } else {
                return {
                    type: 'multiple-choice',
                    question: q.question,
                    answers: this.shuffleArray([q.correctAnswer, ...q.distractors]),
                    correctAnswer: q.correctAnswer,
                    subtopic: q.subtopic,
                    id: q.id
                };
            }
        });

        return questions;
    }

    generateSpanishQuestions(category) {
        let vocabularyToUse = [];

        if (category === 'spanish-all') {
            // Use all Spanish vocabulary
            vocabularyToUse = [
                ...spanishVocabulary.adjectives,
                ...spanishVocabulary.adverbs,
                ...spanishVocabulary.nouns,
                ...spanishVocabulary.verbs
            ];
        } else {
            // Use specific category
            vocabularyToUse = spanishVocabulary[category] || [];
        }

        // Generate 15 questions
        const questions = [];
        const usedWords = new Set();

        for (let i = 0; i < 15 && vocabularyToUse.length > 0; i++) {
            let randomWord;
            let attempts = 0;

            // Find a word we haven't used yet
            do {
                randomWord = vocabularyToUse[Math.floor(Math.random() * vocabularyToUse.length)];
                attempts++;
            } while (usedWords.has(randomWord.spanish) && attempts < 50);

            if (attempts >= 50) break; // Prevent infinite loop

            usedWords.add(randomWord.spanish);

            // Randomly choose question type
            const questionType = Math.random() > 0.5 ? 'multiple-choice' : 'true-false';

            if (questionType === 'multiple-choice') {
                questions.push(this.generateSpanishMultipleChoice(randomWord, vocabularyToUse, category));
            } else {
                questions.push(this.generateSpanishTrueFalse(randomWord, vocabularyToUse, category));
            }
        }

        return this.shuffleArray(questions);
    }

    generateSpanishMultipleChoice(word, allWords, category) {
        // Create distractors from the same category
        const otherWords = allWords.filter(w => w.spanish !== word.spanish);
        const distractors = this.shuffleArray(otherWords)
            .slice(0, 3)
            .map(w => w.english);

        return {
            type: 'multiple-choice',
            question: `What does "${word.spanish}" mean in English?`,
            answers: this.shuffleArray([word.english, ...distractors]),
            correctAnswer: word.english,
            subtopic: category,
            id: `spanish_mc_${word.spanish}`,
            spanishWord: word.spanish,
            actualMeaning: word.english
        };
    }

    generateSpanishTrueFalse(word, allWords, category) {
        const isTrue = Math.random() > 0.5;

        if (isTrue) {
            return {
                type: 'true-false',
                question: `True or False: "${word.spanish}" means "${word.english}" in English`,
                answers: ['True', 'False'],
                correctAnswer: 'True',
                subtopic: category,
                id: `spanish_tf_true_${word.spanish}`,
                spanishWord: word.spanish,
                actualMeaning: word.english
            };
        } else {
            // Create a false statement with a different meaning
            const otherWords = allWords.filter(w => w.spanish !== word.spanish);
            const wrongMeaning = otherWords[Math.floor(Math.random() * otherWords.length)].english;

            return {
                type: 'true-false',
                question: `True or False: "${word.spanish}" means "${wrongMeaning}" in English`,
                answers: ['True', 'False'],
                correctAnswer: 'False',
                subtopic: category,
                id: `spanish_tf_false_${word.spanish}`,
                spanishWord: word.spanish,
                actualMeaning: word.english,
                wrongMeaning: wrongMeaning
            };
        }
    }

    generateChemistryQuestions(topic) {
        let contentToUse = [];

        if (topic === 'chemistry-all') {
            // Use all chemistry topics
            contentToUse = Object.values(chemistryData);
        } else {
            // Use specific topic
            contentToUse = [chemistryData[topic]];
        }

        // Generate questions from content
        const questions = [];
        const usedContent = new Set();

        for (const topicData of contentToUse) {
            for (const subtopic of topicData.subtopics) {
                for (const contentItem of subtopic.content) {
                    if (questions.length >= 15) break;

                    // Create unique ID for this content
                    const contentId = `${subtopic.id}_${contentItem.substring(0, 20)}`;
                    if (usedContent.has(contentId)) continue;
                    usedContent.add(contentId);

                    // Randomly choose question type
                    const questionType = Math.random() > 0.5 ? 'multiple-choice' : 'true-false';

                    if (questionType === 'multiple-choice') {
                        questions.push(this.generateChemistryMultipleChoice(contentItem, subtopic, topicData.title));
                    } else {
                        questions.push(this.generateChemistryTrueFalse(contentItem, subtopic, topicData.title));
                    }
                }
                if (questions.length >= 15) break;
            }
            if (questions.length >= 15) break;
        }

        return this.shuffleArray(questions);
    }

    generateChemistryMultipleChoice(content, subtopic, topicTitle) {
        // Create specific questions based on subtopic and content
        const questionData = this.getChemistryQuestionData(subtopic.title, content);

        if (questionData) {
            return {
                type: 'multiple-choice',
                question: questionData.question,
                answers: this.shuffleArray([questionData.correctAnswer, ...questionData.distractors]),
                correctAnswer: questionData.correctAnswer,
                subtopic: subtopic.title,
                id: `chemistry_mc_${subtopic.id}_${Math.random().toString(36).substr(2, 9)}`,
                topicTitle: topicTitle
            };
        }

        // Fallback to a simpler question if no specific pattern matches
        const sentences = content.split('. ');
        const mainSentence = sentences[0];

        return {
            type: 'multiple-choice',
            question: `Which statement about ${subtopic.title.toLowerCase()} is correct?`,
            answers: this.shuffleArray([
                mainSentence,
                this.generatePlausibleChemistryDistractor(subtopic.title, 1),
                this.generatePlausibleChemistryDistractor(subtopic.title, 2),
                this.generatePlausibleChemistryDistractor(subtopic.title, 3)
            ]),
            correctAnswer: mainSentence,
            subtopic: subtopic.title,
            id: `chemistry_mc_${subtopic.id}_${Math.random().toString(36).substr(2, 9)}`,
            topicTitle: topicTitle
        };
    }

    generateChemistryTrueFalse(content, subtopic, topicTitle) {
        // Get specific true/false statements for chemistry topics
        const trueFalseData = this.getChemistryTrueFalseData(subtopic.title);

        if (trueFalseData) {
            const randomStatement = trueFalseData[Math.floor(Math.random() * trueFalseData.length)];
            return {
                type: 'true-false',
                question: `True or False: ${randomStatement.statement}`,
                answers: ['True', 'False'],
                correctAnswer: randomStatement.isTrue ? 'True' : 'False',
                subtopic: subtopic.title,
                id: `chemistry_tf_${subtopic.id}_${Math.random().toString(36).substr(2, 9)}`,
                topicTitle: topicTitle,
                originalStatement: randomStatement.isTrue ? randomStatement.statement : randomStatement.trueVersion
            };
        }

        // Fallback to content-based true/false
        const sentences = content.split('. ');
        const mainSentence = sentences[0];
        const isTrue = Math.random() > 0.3; // 70% chance of true statements

        if (isTrue) {
            return {
                type: 'true-false',
                question: `True or False: ${mainSentence}`,
                answers: ['True', 'False'],
                correctAnswer: 'True',
                subtopic: subtopic.title,
                id: `chemistry_tf_true_${subtopic.id}_${Math.random().toString(36).substr(2, 9)}`,
                topicTitle: topicTitle
            };
        } else {
            // Create a more sensible false statement
            let falseStatement = this.createSensibleFalseStatement(mainSentence, subtopic.title);

            return {
                type: 'true-false',
                question: `True or False: ${falseStatement}`,
                answers: ['True', 'False'],
                correctAnswer: 'False',
                subtopic: subtopic.title,
                id: `chemistry_tf_false_${subtopic.id}_${Math.random().toString(36).substr(2, 9)}`,
                topicTitle: topicTitle,
                originalStatement: mainSentence
            };
        }
    }

    getChemistryQuestionData(subtopic, content) {
        const questions = {
            "Rate of Reaction": [
                {
                    question: "What is the formula for calculating mean rate of reaction?",
                    correctAnswer: "Mean rate = Quantity of reactant used ÷ Time taken",
                    distractors: [
                        "Mean rate = Time taken ÷ Quantity of reactant used",
                        "Mean rate = Quantity of product × Time taken",
                        "Mean rate = Temperature ÷ Concentration"
                    ]
                },
                {
                    question: "Which units are used for measuring rate of reaction?",
                    correctAnswer: "g/s or cm³/s",
                    distractors: [
                        "g/mol or cm³/mol",
                        "mol/g or s/cm³",
                        "°C/s or atm/min"
                    ]
                },
                {
                    question: "What factors affect the rate of chemical reactions?",
                    correctAnswer: "Concentration, pressure, surface area, temperature, catalysts",
                    distractors: [
                        "Only temperature and pressure",
                        "Only concentration and catalysts",
                        "Only surface area and volume"
                    ]
                },
                {
                    question: "According to collision theory, when do reactions occur?",
                    correctAnswer: "When particles collide with sufficient energy (activation energy)",
                    distractors: [
                        "When particles are at room temperature",
                        "When particles are in a liquid state",
                        "When particles have the same mass"
                    ]
                },
                {
                    question: "How do catalysts affect reaction rates?",
                    correctAnswer: "They provide a different pathway with lower activation energy",
                    distractors: [
                        "They increase the temperature of the reaction",
                        "They increase the concentration of reactants",
                        "They are consumed during the reaction"
                    ]
                }
            ],
            "Reversible Reactions and Dynamic Equilibrium": [
                {
                    question: "What symbol is used to represent reversible reactions?",
                    correctAnswer: "⇌",
                    distractors: ["→", "←", "↔"]
                },
                {
                    question: "What happens at dynamic equilibrium?",
                    correctAnswer: "Forward and reverse reactions occur at the same rate",
                    distractors: [
                        "The reaction stops completely",
                        "Only the forward reaction occurs",
                        "The temperature becomes constant"
                    ]
                },
                {
                    question: "According to Le Chatelier's Principle, how does a system respond to changes?",
                    correctAnswer: "The system shifts to counteract the change",
                    distractors: [
                        "The system amplifies the change",
                        "The system stops reacting",
                        "The system reverses completely"
                    ]
                },
                {
                    question: "How does increasing temperature affect an exothermic equilibrium?",
                    correctAnswer: "It shifts the equilibrium to favor the endothermic direction",
                    distractors: [
                        "It shifts the equilibrium to favor the exothermic direction",
                        "It has no effect on the equilibrium",
                        "It stops the reaction completely"
                    ]
                }
            ],
            "Carbon Compounds as Fuels and Feedstock": [
                {
                    question: "What is the general formula for alkanes?",
                    correctAnswer: "CₙH₂ₙ₊₂",
                    distractors: ["CₙH₂ₙ", "CₙH₂ₙ₋₂", "CₙH₄ₙ"]
                },
                {
                    question: "What are the first four alkanes?",
                    correctAnswer: "Methane, ethane, propane, butane",
                    distractors: [
                        "Ethene, propene, butene, pentene",
                        "Methanol, ethanol, propanol, butanol",
                        "Benzene, toluene, xylene, styrene"
                    ]
                },
                {
                    question: "How does fractional distillation separate hydrocarbons?",
                    correctAnswer: "By evaporation and condensation based on boiling points",
                    distractors: [
                        "By filtering based on molecular size",
                        "By chemical reactions with acids",
                        "By magnetic separation"
                    ]
                },
                {
                    question: "What happens to hydrocarbon properties as molecular size increases?",
                    correctAnswer: "Higher boiling point, higher viscosity, lower flammability",
                    distractors: [
                        "Lower boiling point, lower viscosity, higher flammability",
                        "No change in any properties",
                        "Higher boiling point, lower viscosity, higher flammability"
                    ]
                },
                {
                    question: "What is the test for alkenes?",
                    correctAnswer: "Bromine water changes from orange to colorless",
                    distractors: [
                        "Limewater turns milky",
                        "Litmus paper is bleached white",
                        "A burning splint produces a pop sound"
                    ]
                }
            ],
            "Purity, Formulations, and Chromatography": [
                {
                    question: "What is a pure substance?",
                    correctAnswer: "A single element or compound with specific melting and boiling points",
                    distractors: [
                        "A mixture of different compounds",
                        "Any substance that is colorless",
                        "A substance that dissolves in water"
                    ]
                },
                {
                    question: "How is the Rf value calculated in chromatography?",
                    correctAnswer: "Distance moved by substance ÷ Distance moved by solvent",
                    distractors: [
                        "Distance moved by solvent ÷ Distance moved by substance",
                        "Distance moved by substance × Distance moved by solvent",
                        "Time taken ÷ Distance moved by substance"
                    ]
                },
                {
                    question: "What does a single spot in chromatography indicate?",
                    correctAnswer: "The substance is pure",
                    distractors: [
                        "The substance is impure",
                        "The experiment failed",
                        "The solvent is contaminated"
                    ]
                }
            ],
            "Identification of Common Gases": [
                {
                    question: "What is the test for hydrogen gas?",
                    correctAnswer: "Burning splint produces a pop sound",
                    distractors: [
                        "Glowing splint relights",
                        "Limewater turns milky",
                        "Litmus paper is bleached"
                    ]
                },
                {
                    question: "What is the test for oxygen gas?",
                    correctAnswer: "Glowing splint relights",
                    distractors: [
                        "Burning splint produces a pop sound",
                        "Limewater turns milky",
                        "Litmus paper is bleached"
                    ]
                },
                {
                    question: "What is the test for carbon dioxide?",
                    correctAnswer: "Limewater turns milky (cloudy)",
                    distractors: [
                        "Burning splint produces a pop sound",
                        "Glowing splint relights",
                        "Litmus paper is bleached"
                    ]
                },
                {
                    question: "What is the test for chlorine gas?",
                    correctAnswer: "Damp litmus paper is bleached white",
                    distractors: [
                        "Burning splint produces a pop sound",
                        "Glowing splint relights",
                        "Limewater turns milky"
                    ]
                }
            ],
            "Composition and Evolution of the Atmosphere": [
                {
                    question: "What is the approximate composition of the current atmosphere?",
                    correctAnswer: "~80% nitrogen, ~20% oxygen",
                    distractors: [
                        "~50% nitrogen, ~50% oxygen",
                        "~60% oxygen, ~40% nitrogen",
                        "~90% oxygen, ~10% nitrogen"
                    ]
                },
                {
                    question: "What was the main gas in Earth's early atmosphere?",
                    correctAnswer: "Carbon dioxide",
                    distractors: ["Oxygen", "Nitrogen", "Methane"]
                },
                {
                    question: "How did oxygen levels increase in the atmosphere?",
                    correctAnswer: "Through photosynthesis by algae and plants",
                    distractors: [
                        "Through volcanic activity",
                        "Through meteor impacts",
                        "Through chemical weathering"
                    ]
                }
            ],
            "Greenhouse Gases and Climate Change": [
                {
                    question: "Which gases are greenhouse gases?",
                    correctAnswer: "Water vapor, carbon dioxide, methane",
                    distractors: [
                        "Nitrogen, oxygen, argon",
                        "Only carbon dioxide",
                        "Hydrogen, helium, neon"
                    ]
                },
                {
                    question: "What is a carbon footprint?",
                    correctAnswer: "Total greenhouse gases emitted over a product's life cycle",
                    distractors: [
                        "Only the carbon dioxide produced",
                        "The weight of carbon in a product",
                        "The amount of coal used in production"
                    ]
                }
            ],
            "Atmospheric Pollutants": [
                {
                    question: "Which pollutants are produced by burning fossil fuels?",
                    correctAnswer: "Carbon monoxide, sulfur dioxide, nitrogen oxides, particulates",
                    distractors: [
                        "Only carbon dioxide",
                        "Only water vapor",
                        "Only oxygen and nitrogen"
                    ]
                },
                {
                    question: "What health problems do particulates cause?",
                    correctAnswer: "Respiratory problems and global dimming",
                    distractors: [
                        "Only skin irritation",
                        "Only eye problems",
                        "No health problems"
                    ]
                }
            ],
            "Using Earth's Resources and Potable Water": [
                {
                    question: "What is potable water?",
                    correctAnswer: "Water that is safe to drink",
                    distractors: [
                        "Water that is completely pure",
                        "Water that contains no dissolved substances",
                        "Water that is hot"
                    ]
                },
                {
                    question: "What methods are used for desalination?",
                    correctAnswer: "Distillation or reverse osmosis",
                    distractors: [
                        "Only filtration",
                        "Only boiling",
                        "Only adding chemicals"
                    ]
                }
            ],
            "Life Cycle Assessment and Recycling": [
                {
                    question: "What does a Life Cycle Assessment (LCA) evaluate?",
                    correctAnswer: "Environmental impact from raw materials to disposal",
                    distractors: [
                        "Only the manufacturing process",
                        "Only the disposal stage",
                        "Only the cost of production"
                    ]
                },
                {
                    question: "How does recycling help the environment?",
                    correctAnswer: "Reduces resource use, energy consumption, and waste",
                    distractors: [
                        "Only reduces waste",
                        "Only saves money",
                        "Has no environmental benefit"
                    ]
                }
            ],
            "Fundamental Concepts": [
                {
                    question: "How do atoms bond together?",
                    correctAnswer: "By transferring electrons (ionic) or sharing electrons (covalent)",
                    distractors: [
                        "Only by sharing protons",
                        "Only by magnetic attraction",
                        "Only at high temperatures"
                    ]
                },
                {
                    question: "What is conserved in chemical reactions?",
                    correctAnswer: "Energy is conserved - neither created nor destroyed",
                    distractors: [
                        "Only mass is conserved",
                        "Nothing is conserved",
                        "Only the number of atoms"
                    ]
                }
            ]
        };

        const topicQuestions = questions[subtopic];
        if (topicQuestions && topicQuestions.length > 0) {
            return topicQuestions[Math.floor(Math.random() * topicQuestions.length)];
        }
        return null;
    }

    generatePlausibleChemistryDistractor(subtopic, index) {
        const distractors = {
            "Rate of Reaction": [
                "Rate depends only on the color of reactants",
                "Catalysts are always consumed in reactions",
                "Temperature has no effect on reaction rates"
            ],
            "Reversible Reactions and Dynamic Equilibrium": [
                "Equilibrium means the reaction has stopped",
                "Le Chatelier's principle only applies to gases",
                "Reversible reactions only occur at high temperatures"
            ],
            "Carbon Compounds as Fuels and Feedstock": [
                "All hydrocarbons have the same boiling point",
                "Alkanes are more reactive than alkenes",
                "Fractional distillation separates by color"
            ],
            "Purity, Formulations, and Chromatography": [
                "Pure substances have variable melting points",
                "Rf values are always greater than 1",
                "Chromatography only works with colored substances"
            ],
            "Identification of Common Gases": [
                "All gases produce the same test results",
                "Gas tests only work at room temperature",
                "Hydrogen and oxygen give identical test results"
            ],
            "Composition and Evolution of the Atmosphere": [
                "The atmosphere has never changed composition",
                "Oxygen was always the main atmospheric gas",
                "Volcanic activity had no effect on early atmosphere"
            ],
            "Greenhouse Gases and Climate Change": [
                "Greenhouse gases cool the Earth",
                "Only human activities produce greenhouse gases",
                "Climate change is not related to greenhouse gases"
            ],
            "Atmospheric Pollutants": [
                "All combustion products are beneficial",
                "Pollutants only affect the environment, not health",
                "Fossil fuels produce no harmful emissions"
            ],
            "Using Earth's Resources and Potable Water": [
                "All water is naturally potable",
                "Desalination requires no energy",
                "Waste water treatment is unnecessary"
            ],
            "Life Cycle Assessment and Recycling": [
                "LCA only considers manufacturing costs",
                "Recycling has no environmental benefits",
                "Products have no environmental impact"
            ],
            "Fundamental Concepts": [
                "Atoms cannot bond with each other",
                "Energy can be created in reactions",
                "Chemical reactions violate conservation laws"
            ]
        };

        const topicDistractors = distractors[subtopic] || [
            "This is not a chemistry concept",
            "This only applies to physics",
            "This is related to biology only"
        ];

        return topicDistractors[index % topicDistractors.length];
    }

    getChemistryTrueFalseData(subtopic) {
        const trueFalseStatements = {
            "Rate of Reaction": [
                { statement: "Rate of reaction is measured in g/s or cm³/s", isTrue: true },
                { statement: "Catalysts are consumed during chemical reactions", isTrue: false, trueVersion: "Catalysts are not consumed during chemical reactions" },
                { statement: "Increasing temperature always increases reaction rate", isTrue: true },
                { statement: "Surface area has no effect on reaction rate", isTrue: false, trueVersion: "Surface area affects reaction rate" },
                { statement: "Collision theory states that particles must collide with sufficient energy", isTrue: true },
                { statement: "Higher concentration decreases reaction rate", isTrue: false, trueVersion: "Higher concentration increases reaction rate" }
            ],
            "Reversible Reactions and Dynamic Equilibrium": [
                { statement: "Reversible reactions are represented by the symbol ⇌", isTrue: true },
                { statement: "At equilibrium, the forward reaction stops", isTrue: false, trueVersion: "At equilibrium, forward and reverse reactions occur at the same rate" },
                { statement: "Le Chatelier's Principle predicts how equilibrium responds to changes", isTrue: true },
                { statement: "Increasing pressure always shifts equilibrium to the right", isTrue: false, trueVersion: "Increasing pressure shifts equilibrium to the side with fewer gas molecules" },
                { statement: "If a reaction is exothermic in one direction, it is endothermic in the reverse", isTrue: true }
            ],
            "Carbon Compounds as Fuels and Feedstock": [
                { statement: "The general formula for alkanes is CₙH₂ₙ₊₂", isTrue: true },
                { statement: "Methane is the first alkane", isTrue: true },
                { statement: "Larger hydrocarbon molecules have lower boiling points", isTrue: false, trueVersion: "Larger hydrocarbon molecules have higher boiling points" },
                { statement: "Alkenes are less reactive than alkanes", isTrue: false, trueVersion: "Alkenes are more reactive than alkanes" },
                { statement: "Fractional distillation separates hydrocarbons by boiling point", isTrue: true },
                { statement: "Bromine water test turns from colorless to orange with alkenes", isTrue: false, trueVersion: "Bromine water test turns from orange to colorless with alkenes" }
            ],
            "Purity, Formulations, and Chromatography": [
                { statement: "Pure substances have specific melting and boiling points", isTrue: true },
                { statement: "Rf value equals distance moved by substance divided by distance moved by solvent", isTrue: true },
                { statement: "Pure compounds produce multiple spots in chromatography", isTrue: false, trueVersion: "Pure compounds produce single spots in chromatography" },
                { statement: "Formulations are mixtures designed for specific purposes", isTrue: true },
                { statement: "Rf values can be greater than 1", isTrue: false, trueVersion: "Rf values are always between 0 and 1" }
            ],
            "Identification of Common Gases": [
                { statement: "Hydrogen gas produces a pop sound with a burning splint", isTrue: true },
                { statement: "Oxygen gas relights a glowing splint", isTrue: true },
                { statement: "Carbon dioxide turns limewater milky", isTrue: true },
                { statement: "Chlorine gas bleaches damp litmus paper", isTrue: true },
                { statement: "Hydrogen and oxygen produce the same test results", isTrue: false, trueVersion: "Hydrogen and oxygen produce different test results" }
            ],
            "Composition and Evolution of the Atmosphere": [
                { statement: "Current atmosphere is approximately 80% nitrogen and 20% oxygen", isTrue: true },
                { statement: "Early atmosphere contained mainly oxygen", isTrue: false, trueVersion: "Early atmosphere contained mainly carbon dioxide" },
                { statement: "Photosynthesis increased oxygen levels in the atmosphere", isTrue: true },
                { statement: "The atmosphere composition has remained constant for billions of years", isTrue: false, trueVersion: "The atmosphere composition has changed significantly over billions of years" }
            ],
            "Greenhouse Gases and Climate Change": [
                { statement: "Greenhouse gases trap heat in the atmosphere", isTrue: true },
                { statement: "Carbon dioxide is the only greenhouse gas", isTrue: false, trueVersion: "Water vapor, carbon dioxide, and methane are all greenhouse gases" },
                { statement: "Human activities increase greenhouse gas concentrations", isTrue: true },
                { statement: "Carbon footprint measures only carbon dioxide emissions", isTrue: false, trueVersion: "Carbon footprint measures total greenhouse gas emissions" }
            ],
            "Atmospheric Pollutants": [
                { statement: "Carbon monoxide is toxic and colorless", isTrue: true },
                { statement: "Sulfur dioxide causes acid rain", isTrue: true },
                { statement: "Particulates cause global dimming", isTrue: true },
                { statement: "All combustion products are harmless", isTrue: false, trueVersion: "Some combustion products are harmful pollutants" }
            ],
            "Using Earth's Resources and Potable Water": [
                { statement: "Potable water is safe to drink", isTrue: true },
                { statement: "Desalination requires large amounts of energy", isTrue: true },
                { statement: "All natural water sources are potable", isTrue: false, trueVersion: "Not all natural water sources are potable" },
                { statement: "Waste water treatment involves screening and sedimentation", isTrue: true }
            ],
            "Life Cycle Assessment and Recycling": [
                { statement: "LCA evaluates environmental impact from raw materials to disposal", isTrue: true },
                { statement: "Recycling reduces resource use and energy consumption", isTrue: true },
                { statement: "LCA only considers the manufacturing stage", isTrue: false, trueVersion: "LCA considers all stages from raw materials to disposal" },
                { statement: "Recycling has no environmental benefits", isTrue: false, trueVersion: "Recycling has significant environmental benefits" }
            ],
            "Fundamental Concepts": [
                { statement: "Atoms bond by transferring or sharing electrons", isTrue: true },
                { statement: "Energy is conserved in chemical reactions", isTrue: true },
                { statement: "Chemical reactions can create or destroy energy", isTrue: false, trueVersion: "Energy is conserved in chemical reactions - neither created nor destroyed" },
                { statement: "There are approximately 100 naturally occurring elements", isTrue: true }
            ]
        };

        return trueFalseStatements[subtopic] || null;
    }

    createSensibleFalseStatement(trueStatement, subtopic) {
        // Create more sensible false statements based on common misconceptions
        const falseVersions = {
            "Rate of Reaction": [
                "Rate of reaction is measured in mol/L",
                "Catalysts slow down chemical reactions",
                "Temperature has no effect on reaction rate"
            ],
            "Reversible Reactions and Dynamic Equilibrium": [
                "Equilibrium means the reaction has stopped",
                "Le Chatelier's principle only applies to temperature changes",
                "Reversible reactions only go in one direction"
            ],
            "Carbon Compounds as Fuels and Feedstock": [
                "All hydrocarbons have the same properties",
                "Alkenes are less reactive than alkanes",
                "Fractional distillation separates by molecular weight only"
            ]
        };

        const topicFalseVersions = falseVersions[subtopic];
        if (topicFalseVersions) {
            return topicFalseVersions[Math.floor(Math.random() * topicFalseVersions.length)];
        }

        // Fallback: simple negation
        return `It is not true that ${trueStatement.toLowerCase()}`;
    }

    getMultipleChoiceQuestions() {
        return [
            // Weimar Republic questions
            {
                id: "mc_1",
                type: "multiple-choice",
                question: "On what specific date did Kaiser Wilhelm II abdicate?",
                correctAnswer: "9 November 1918",
                distractors: ["11 November 1918", "8 November 1918", "10 November 1918"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "mc_2",
                type: "multiple-choice",
                question: "Who drafted the Weimar Constitution?",
                correctAnswer: "Hugo Preuss",
                distractors: ["Gustav Stresemann", "Matthias Erzberger", "Friedrich Ebert"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "mc_3",
                type: "multiple-choice",
                question: "How much were the reparations imposed by the Treaty of Versailles?",
                correctAnswer: "£6.6 billion",
                distractors: ["£4.2 billion", "£8.8 billion", "£5.5 billion"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_4",
                type: "multiple-choice",
                question: "What was the exchange rate at the peak of hyperinflation in November 1923?",
                correctAnswer: "1 USD = 4.2 trillion marks",
                distractors: ["1 USD = 2.1 trillion marks", "1 USD = 6.8 trillion marks", "1 USD = 3.5 trillion marks"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_5",
                type: "multiple-choice",
                question: "How many Freikorps participated in the Kapp Putsch?",
                correctAnswer: "5,000",
                distractors: ["3,000", "7,000", "10,000"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_6",
                type: "multiple-choice",
                question: "On what date was the Rentenmark introduced?",
                correctAnswer: "20 November 1923",
                distractors: ["15 November 1923", "25 November 1923", "10 November 1923"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "mc_7",
                type: "multiple-choice",
                question: "How much did the Young Plan reduce reparations to?",
                correctAnswer: "£2 billion",
                distractors: ["£3 billion", "£1.5 billion", "£2.5 billion"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "mc_8",
                type: "multiple-choice",
                question: "What percentage of women voted in the first Weimar election?",
                correctAnswer: "90%",
                distractors: ["85%", "95%", "80%"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Changes in Society, 1924–29"
            },
            // Hitler's Rise to Power questions
            {
                id: "mc_9",
                type: "multiple-choice",
                question: "When was the NSDAP (Nazi Party) officially renamed from the German Workers' Party?",
                correctAnswer: "February 1920",
                distractors: ["January 1920", "March 1920", "April 1920"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "mc_10",
                type: "multiple-choice",
                question: "How many Nazis were killed during the Munich Putsch?",
                correctAnswer: "16",
                distractors: ["12", "20", "18"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "mc_11",
                type: "multiple-choice",
                question: "How many people were unemployed in Germany by 1932?",
                correctAnswer: "6 million",
                distractors: ["4 million", "8 million", "5 million"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Growth in Support for the Nazis, 1929–32"
            },
            {
                id: "mc_12",
                type: "multiple-choice",
                question: "How many seats did the Nazis win in July 1932?",
                correctAnswer: "230 seats",
                distractors: ["196 seats", "250 seats", "210 seats"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "How Hitler Became Chancellor, 1932–33"
            },
            // Nazi Control questions
            {
                id: "mc_13",
                type: "multiple-choice",
                question: "Who was blamed for the Reichstag Fire?",
                correctAnswer: "Marinus van der Lubbe",
                distractors: ["Karl Liebknecht", "Rosa Luxemburg", "Ernst Thälmann"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "mc_14",
                type: "multiple-choice",
                question: "How many people were killed during the Night of the Long Knives?",
                correctAnswer: "Over 400",
                distractors: ["Over 200", "Over 600", "Over 300"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "mc_15",
                type: "multiple-choice",
                question: "Who led the Gestapo?",
                correctAnswer: "Reinhard Heydrich",
                distractors: ["Heinrich Himmler", "Roland Freisler", "Rudolf Hess"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "The Police State"
            },
            {
                id: "mc_16",
                type: "multiple-choice",
                question: "How much did the Volksempfänger radio cost?",
                correctAnswer: "76 Reichsmarks",
                distractors: ["65 Reichsmarks", "85 Reichsmarks", "70 Reichsmarks"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            // Life in Nazi Germany questions
            {
                id: "mc_17",
                type: "multiple-choice",
                question: "How much was the marriage loan under the Law for Encouragement of Marriage?",
                correctAnswer: "1,000 Reichsmarks",
                distractors: ["800 Reichsmarks", "1,200 Reichsmarks", "750 Reichsmarks"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards Women"
            },
            {
                id: "mc_18",
                type: "multiple-choice",
                question: "From what year was Hitler Youth membership compulsory?",
                correctAnswer: "1936",
                distractors: ["1933", "1935", "1937"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards the Young"
            },
            {
                id: "mc_19",
                type: "multiple-choice",
                question: "How much did unemployment fall from 1929 to 1939?",
                correctAnswer: "From 6.1 million to 0.5 million",
                distractors: ["From 5.5 million to 0.3 million", "From 7.2 million to 0.8 million", "From 6.8 million to 0.6 million"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Employment and Living Standards"
            },
            {
                id: "mc_20",
                type: "multiple-choice",
                question: "How many synagogues were destroyed during Kristallnacht?",
                correctAnswer: "206",
                distractors: ["150", "250", "180"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            }
        ];
    }

    getTrueFalseStatements() {
        return [
            // Weimar Republic statements
            {
                id: "tf_1",
                type: "true-false",
                statement: "Kaiser Wilhelm II abdicated on 9 November 1918",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "tf_2",
                type: "true-false",
                statement: "Hugo Preuss drafted the Weimar Constitution",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "tf_3",
                type: "true-false",
                statement: "The Treaty of Versailles imposed £6.6 billion in reparations",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_4",
                type: "true-false",
                statement: "At the peak of hyperinflation, 1 USD equaled 4.2 trillion marks",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_4",
                type: "true-false",
                statement: "The Spartacist Uprising was led by Rosa Luxemburg in 1919",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_5",
                type: "true-false",
                statement: "The Kapp Putsch was successful in overthrowing the government",
                isTrue: false,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_6",
                type: "true-false",
                statement: "The French occupied the Ruhr in 1923",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_7",
                type: "true-false",
                statement: "Gustav Stresemann introduced the Rentenmark in 1924",
                isTrue: false,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "tf_8",
                type: "true-false",
                statement: "Germany joined the League of Nations in 1926",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },

            // Hitler's Rise to Power statements
            {
                id: "tf_9",
                type: "true-false",
                statement: "Hitler joined the German Workers' Party in 1919",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "tf_10",
                type: "true-false",
                statement: "The SA were also known as Brownshirts",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "tf_11",
                type: "true-false",
                statement: "The Munich Putsch was successful in 1923",
                isTrue: false,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "tf_12",
                type: "true-false",
                statement: "Hitler wrote Mein Kampf while in prison",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "tf_13",
                type: "true-false",
                statement: "The Wall Street Crash occurred in 1929",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Growth in Support for the Nazis, 1929–32"
            },
            {
                id: "tf_14",
                type: "true-false",
                statement: "Hitler became Chancellor in January 1934",
                isTrue: false,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "How Hitler Became Chancellor, 1932–33"
            },

            // Nazi Control statements
            {
                id: "tf_15",
                type: "true-false",
                statement: "The Reichstag Fire occurred in February 1933",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_16",
                type: "true-false",
                statement: "The Enabling Act was passed in March 1933",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_17",
                type: "true-false",
                statement: "The Night of the Long Knives occurred in 1935",
                isTrue: false,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_18",
                type: "true-false",
                statement: "The Gestapo was the Nazi secret police",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "The Police State"
            },
            {
                id: "tf_19",
                type: "true-false",
                statement: "Joseph Goebbels was in charge of propaganda",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            {
                id: "tf_20",
                type: "true-false",
                statement: "The Volksempfänger radio cost 76 Reichsmarks",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            {
                id: "tf_21",
                type: "true-false",
                statement: "70% of German households owned a radio by 1939",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },

            // Life in Nazi Germany statements
            {
                id: "tf_22",
                type: "true-false",
                statement: "The Law for Encouragement of Marriage provided 1,000 Reichsmark loans",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards Women"
            },
            {
                id: "tf_23",
                type: "true-false",
                statement: "Hitler Youth membership became compulsory in 1936",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards the Young"
            },
            {
                id: "tf_24",
                type: "true-false",
                statement: "Unemployment fell from 6.1 million in 1929 to 0.5 million in 1939",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Employment and Living Standards"
            },
            {
                id: "tf_25",
                type: "true-false",
                statement: "206 synagogues were destroyed during Kristallnacht",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            },
            {
                id: "tf_26",
                type: "true-false",
                statement: "8,000 homosexuals were imprisoned by 1938",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            }
        ];
    }

    generateDistractors(correctContent, subtopic) {
        // Generate plausible but incorrect answers
        const distractors = [
            "This event occurred during the French Revolution",
            "This was primarily a British policy initiative",
            "This happened after World War II ended",
            "This was a result of American intervention",
            "This occurred during the Industrial Revolution"
        ];
        
        return this.shuffleArray(distractors).slice(0, 3);
    }

    displayQuestion() {
        const question = this.questions[this.currentQuestionIndex];

        // Clear any existing explanation
        const existingExplanation = document.querySelector('.explanation');
        if (existingExplanation) {
            existingExplanation.remove();
        }

        // Clear any auto-progress timer
        this.clearAutoProgress();

        // Update progress
        const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        document.querySelector('.progress-fill').style.width = `${progress}%`;

        // Update counters
        document.getElementById('question-counter').textContent =
            `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
        document.getElementById('score').textContent =
            `Score: ${this.score}/${this.questions.length}`;

        // Display question
        document.getElementById('question-text').textContent = question.question;
        document.getElementById('question-type').textContent =
            question.type === 'multiple-choice' ? 'Multiple Choice' : 'True/False';

        // Display answers
        const answersContainer = document.getElementById('answers-container');
        answersContainer.innerHTML = '';

        question.answers.forEach((answer, index) => {
            const button = document.createElement('button');
            button.className = 'answer-option';
            button.textContent = answer;
            button.addEventListener('click', () => this.selectAnswer(answer, button));
            answersContainer.appendChild(button);
        });

        // Reset state
        this.selectedAnswer = null;
        this.questionAnswered = false;
        document.getElementById('next-btn').disabled = true;
        document.getElementById('next-btn').textContent = 'Next Question';
    }

    selectAnswer(answer, buttonElement) {
        if (this.questionAnswered) return;
        
        // Clear previous selections
        document.querySelectorAll('.answer-option').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        // Mark selected
        buttonElement.classList.add('selected');
        this.selectedAnswer = answer;
        
        // Check answer immediately
        this.checkAnswer();
    }

    checkAnswer() {
        const question = this.questions[this.currentQuestionIndex];
        const isCorrect = this.selectedAnswer === question.correctAnswer;

        if (isCorrect) {
            this.score++;
        }

        // Show correct/incorrect styling
        document.querySelectorAll('.answer-option').forEach(btn => {
            if (btn.textContent === question.correctAnswer) {
                btn.classList.add('correct');
            } else if (btn.textContent === this.selectedAnswer && !isCorrect) {
                btn.classList.add('incorrect');
            }
            btn.style.pointerEvents = 'none';
        });

        // Always show explanation for educational value
        this.showExplanation(question, isCorrect);

        this.questionAnswered = true;
        document.getElementById('next-btn').disabled = false;

        // Update score display
        document.getElementById('score').textContent =
            `Score: ${this.score}/${this.currentQuestionIndex + 1}`;

        // Auto-progress after 4 seconds if answer is correct and auto-skip is enabled
        if (isCorrect && this.isAutoSkipEnabled()) {
            this.startAutoProgress();
        }
    }

    startAutoProgress() {
        // Clear any existing timer
        if (this.autoProgressTimer) {
            clearTimeout(this.autoProgressTimer);
        }

        // Show countdown on next button
        this.showCountdown();

        // Set timer for auto-progression
        this.autoProgressTimer = setTimeout(() => {
            this.nextQuestion();
        }, 4000);
    }

    showCountdown() {
        const nextBtn = document.getElementById('next-btn');
        const originalText = nextBtn.textContent;
        let countdown = 4;

        const updateCountdown = () => {
            nextBtn.textContent = `Next Question (${countdown})`;
            countdown--;

            if (countdown >= 0) {
                setTimeout(updateCountdown, 1000);
            }
        };

        updateCountdown();
    }

    clearAutoProgress() {
        if (this.autoProgressTimer) {
            clearTimeout(this.autoProgressTimer);
            this.autoProgressTimer = null;
        }

        // Reset button text
        const nextBtn = document.getElementById('next-btn');
        nextBtn.textContent = 'Next Question';
    }

    showExplanation(question, isCorrect) {
        // Remove any existing explanation
        const existingExplanation = document.querySelector('.explanation');
        if (existingExplanation) {
            existingExplanation.remove();
        }

        const explanation = document.createElement('div');
        explanation.className = isCorrect ? 'explanation correct-explanation' : 'explanation incorrect-explanation';

        let explanationText = '';

        if (question.explanation) {
            explanationText = question.explanation;
        } else {
            // Generate explanation based on question content
            explanationText = this.generateExplanation(question, isCorrect);
        }

        const headerIcon = isCorrect ? '✅' : '📚';
        const headerText = isCorrect ? 'Excellent! Here\'s more context:' : 'Learn More:';

        explanation.innerHTML = `
            <div class="explanation-header">
                <strong>${headerIcon} ${headerText}</strong>
            </div>
            <div class="explanation-content">
                ${explanationText}
            </div>
        `;

        // Insert explanation after the answers container
        const answersContainer = document.getElementById('answers-container');
        answersContainer.parentNode.insertBefore(explanation, answersContainer.nextSibling);

        // Auto-scroll to ensure the explanation and buttons are visible
        this.scrollToExplanation(explanation);
    }

    scrollToExplanation(explanationElement) {
        // Wait for the explanation to be rendered and animations to complete
        setTimeout(() => {
            // Get the quiz controls (Next Question button area)
            const quizControls = document.querySelector('.quiz-controls');

            if (quizControls && explanationElement) {
                // Calculate the optimal scroll position
                const explanationRect = explanationElement.getBoundingClientRect();
                const controlsRect = quizControls.getBoundingClientRect();
                const viewportHeight = window.innerHeight;

                // Check if both explanation and controls are fully visible
                const explanationVisible = explanationRect.top >= 0 && explanationRect.bottom <= viewportHeight;
                const controlsVisible = controlsRect.top >= 0 && controlsRect.bottom <= viewportHeight;

                // Only scroll if either element is not fully visible
                if (!explanationVisible || !controlsVisible) {
                    // Calculate the ideal scroll position
                    // We want to show the explanation with some top padding
                    // and ensure the controls are visible at the bottom
                    // Adjust padding based on screen size
                    const isMobile = window.innerWidth <= 768;
                    const topPadding = isMobile ? 40 : 60;
                    const bottomPadding = isMobile ? 30 : 20;

                    // Calculate how much space we need
                    const neededHeight = explanationRect.height + controlsRect.height + topPadding + bottomPadding;

                    let targetScrollTop;

                    if (neededHeight <= viewportHeight) {
                        // If everything fits, position explanation with top padding
                        targetScrollTop = window.pageYOffset + explanationRect.top - topPadding;
                    } else {
                        // If content is too tall, prioritize showing the controls (buttons)
                        // Position so controls are visible at bottom with padding
                        targetScrollTop = window.pageYOffset + controlsRect.bottom - viewportHeight + bottomPadding;
                    }

                    // Ensure we don't scroll past the top of the page
                    targetScrollTop = Math.max(0, targetScrollTop);

                    // Smooth scroll to the calculated position
                    window.scrollTo({
                        top: targetScrollTop,
                        behavior: 'smooth'
                    });
                }
            }
        }, 150); // Slightly longer delay to ensure animations complete
    }

    generateExplanation(question, isCorrect) {
        if (this.currentSubject === 'spanish') {
            return this.generateSpanishExplanation(question, isCorrect);
        } else if (this.currentSubject === 'chemistry') {
            return this.generateChemistryExplanation(question, isCorrect);
        }

        // Get additional context based on the question's subtopic
        const explanations = this.getExplanationDatabase();

        // Find relevant explanation
        const relevantExplanation = explanations.find(exp =>
            exp.subtopic === question.subtopic ||
            exp.keywords.some(keyword => question.question.toLowerCase().includes(keyword.toLowerCase()))
        );

        let explanationText = '';

        if (isCorrect) {
            // For correct answers, focus on additional context and related information
            if (relevantExplanation) {
                explanationText = `<strong>Why this is correct:</strong> ${relevantExplanation.explanation}<br><br>
                                 <strong>Additional Context:</strong> ${this.getAdditionalContext(question, relevantExplanation)}`;
            } else {
                explanationText = `<strong>Great job!</strong> This relates to ${question.subtopic}. Your understanding of this historical period is developing well.`;
            }
        } else {
            // For incorrect answers, show correct answer and explanation
            if (relevantExplanation) {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Explanation:</strong> ${relevantExplanation.explanation}`;
            } else {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Context:</strong> This relates to ${question.subtopic}. Review this topic for more details.`;
            }
        }

        return explanationText;
    }

    generateSpanishExplanation(question, isCorrect) {
        // Use stored values from question object when available, otherwise extract from question text
        const spanishWord = question.spanishWord || (question.question.match(/"([^"]+)"/)?.[0]?.replace(/"/g, '') || '');
        const actualMeaning = question.actualMeaning || this.findSpanishWordMeaning(spanishWord);

        let explanationText = '';

        if (isCorrect) {
            if (question.type === 'multiple-choice') {
                // For multiple choice, question.correctAnswer is the English meaning
                explanationText = `<strong>¡Excelente!</strong> You correctly identified the meaning.<br><br>
                                 <strong>Word:</strong> ${spanishWord}<br>
                                 <strong>Meaning:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
            } else {
                // True/False correct - always show actual meaning regardless of True/False answer
                if (question.correctAnswer === 'True') {
                    explanationText = `<strong>¡Correcto!</strong> You correctly identified this as true!<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Meaning:</strong> ${actualMeaning}<br><br>
                                     <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                } else {
                    // They correctly said it was false
                    const wrongMeaning = question.wrongMeaning || (question.question.match(/means "([^"]+)"/)?.[1] || '');

                    explanationText = `<strong>¡Correcto!</strong> You correctly identified this as false!<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Does NOT mean:</strong> ${wrongMeaning}<br>
                                     <strong>Actually means:</strong> ${actualMeaning}<br><br>
                                     <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                }
            }
        } else {
            if (question.type === 'multiple-choice') {
                // For multiple choice, question.correctAnswer is the English meaning
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Word:</strong> ${spanishWord}<br>
                                 <strong>Meaning:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
            } else {
                // True/False incorrect - show the actual meaning
                if (question.correctAnswer === 'True') {
                    // They said false when it was true
                    explanationText = `<strong>Correct Answer:</strong> True<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Actual meaning:</strong> ${actualMeaning}<br><br>
                                     <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                } else {
                    // They said true when it was false
                    const wrongMeaning = question.wrongMeaning || (question.question.match(/means "([^"]+)"/)?.[1] || '');

                    explanationText = `<strong>Correct Answer:</strong> False<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Does NOT mean:</strong> ${wrongMeaning}<br>
                                     <strong>Actually means:</strong> ${actualMeaning}<br><br>
                                     <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                }
            }
        }

        return explanationText;
    }

    findSpanishWordMeaning(spanishWord) {
        // Search through all Spanish vocabulary categories to find the word
        const allCategories = ['adjectives', 'adverbs', 'nouns', 'verbs'];

        for (const category of allCategories) {
            const words = spanishVocabulary[category];
            const foundWord = words.find(word => word.spanish === spanishWord);
            if (foundWord) {
                return foundWord.english;
            }
        }

        return 'meaning not found';
    }

    getSpanishTip(category) {
        const tips = {
            'adjectives': 'Remember that Spanish adjectives usually come after the noun they describe, and they must agree in gender and number.',
            'adverbs': 'Many Spanish adverbs end in -mente (equivalent to -ly in English). Practice using them to describe how actions are performed.',
            'nouns': 'Spanish nouns have gender (masculine/feminine). Learning the gender with each noun will help with adjective agreement.',
            'verbs': 'Spanish verbs change their endings based on who is doing the action. Practice conjugating regular verbs first.',
            'spanish-all': 'Try to use new vocabulary in sentences to help remember the meanings. Context helps with retention!'
        };

        return tips[category] || 'Keep practicing! Regular review helps build vocabulary retention.';
    }

    generateChemistryExplanation(question, isCorrect) {
        let explanationText = '';

        if (isCorrect) {
            explanationText = `<strong>Excellent!</strong> You got it right!<br><br>
                             <strong>Topic:</strong> ${question.subtopic}<br><br>
                             <strong>Key Point:</strong> ${this.getChemistryKeyPoint(question)}<br><br>
                             <strong>Study Tip:</strong> ${this.getChemistryTip(question.subtopic)}`;
        } else {
            if (question.type === 'true-false' && question.originalStatement) {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Topic:</strong> ${question.subtopic}<br><br>
                                 <strong>The correct statement is:</strong> ${question.originalStatement}<br><br>
                                 <strong>Study Tip:</strong> ${this.getChemistryTip(question.subtopic)}`;
            } else {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Topic:</strong> ${question.subtopic}<br><br>
                                 <strong>Key Point:</strong> ${this.getChemistryKeyPoint(question)}<br><br>
                                 <strong>Study Tip:</strong> ${this.getChemistryTip(question.subtopic)}`;
            }
        }

        return explanationText;
    }

    getChemistryKeyPoint(question) {
        // Extract key learning points based on the question content
        const keyPoints = {
            'Rate of Reaction': 'Rate of reaction depends on concentration, temperature, surface area, pressure (for gases), and catalysts. Remember the mnemonic CATS!',
            'Reversible Reactions and Dynamic Equilibrium': 'At equilibrium, forward and reverse reactions occur at the same rate. Le Chatelier\'s principle helps predict how changes affect equilibrium.',
            'Carbon Compounds as Fuels and Feedstock': 'Crude oil is separated by fractional distillation. Longer hydrocarbon chains have higher boiling points and are more viscous.',
            'Purity, Formulations, and Chromatography': 'Pure substances have fixed melting/boiling points. Rf values in chromatography = distance moved by substance ÷ distance moved by solvent.',
            'Identification of Common Gases': 'Remember POCL: Pop (hydrogen), Oxygen relights glowing splint, Cloudy limewater (CO₂), Litmus bleached (chlorine).',
            'Composition and Evolution of the Atmosphere': 'Current atmosphere is ~80% nitrogen, ~20% oxygen. Oxygen increased due to photosynthesis by early plants and algae.',
            'Greenhouse Gases and Climate Change': 'Greenhouse gases (CO₂, methane, water vapor) trap heat. Human activities increase these gases, causing climate change.',
            'Atmospheric Pollutants': 'Burning fossil fuels produces pollutants: CO (toxic), SO₂ (acid rain), NOₓ (respiratory problems), particulates (health issues).',
            'Using Earth\'s Resources and Potable Water': 'Potable water is safe to drink. Treatment involves filtration and sterilization. Desalination uses distillation or reverse osmosis.',
            'Life Cycle Assessment and Recycling': 'LCA assesses environmental impact from raw materials to disposal. Recycling reduces resource use and environmental impact.',
            'Fundamental Concepts': 'Atoms form all matter. Chemical reactions involve electron transfer, sharing, or proton transfer. Energy is conserved in reactions.'
        };

        return keyPoints[question.subtopic] || 'This is an important chemistry concept that builds understanding of chemical processes.';
    }

    getChemistryTip(subtopic) {
        const tips = {
            'Rate of Reaction': 'Practice calculating rates from graphs and data. Remember that catalysts speed up reactions without being used up.',
            'Reversible Reactions and Dynamic Equilibrium': 'Use Le Chatelier\'s principle to predict equilibrium shifts. Practice with temperature, pressure, and concentration changes.',
            'Carbon Compounds as Fuels and Feedstock': 'Learn the first four alkanes: methane, ethane, propane, butane. Practice balancing combustion equations.',
            'Purity, Formulations, and Chromatography': 'Practice calculating Rf values. Remember that pure substances give single spots in chromatography.',
            'Identification of Common Gases': 'Practice the gas tests until they become automatic. Each gas has a unique test result.',
            'Composition and Evolution of the Atmosphere': 'Understand how photosynthesis changed Earth\'s atmosphere over billions of years.',
            'Greenhouse Gases and Climate Change': 'Connect human activities to specific greenhouse gas emissions. Understand both causes and effects.',
            'Atmospheric Pollutants': 'Link specific fuels to their pollutants. Understand both environmental and health impacts.',
            'Using Earth\'s Resources and Potable Water': 'Distinguish between renewable and finite resources. Understand water treatment processes.',
            'Life Cycle Assessment and Recycling': 'Consider all stages of a product\'s life when assessing environmental impact.',
            'Fundamental Concepts': 'These concepts underpin all of chemistry. Make sure you understand atoms, bonding, and energy changes.'
        };

        return tips[subtopic] || 'Review your chemistry notes and practice past paper questions on this topic.';
    }

    getAdditionalContext(question, explanation) {
        // Provide additional related information for correct answers
        const additionalContexts = {
            "Origins of the Republic, 1918–19": "The Weimar Republic's democratic experiment was Germany's first attempt at parliamentary democracy, but it faced immediate challenges from both political extremes.",

            "Early Challenges to the Weimar Republic, 1919–23": "These early crises nearly destroyed the republic and created lasting resentment that extremist parties would later exploit.",

            "Golden Years: Recovery of the Republic, 1924–29": "This period of stability was largely dependent on American loans, making Germany vulnerable when the global economy collapsed in 1929.",

            "Changes in Society, 1924–29": "The cultural flowering of the Weimar period represented a brief moment of creative freedom that would be brutally suppressed under Nazi rule.",

            "Early Development of the Nazi Party, 1920–22": "The Nazi Party started as a small, radical group but Hitler's oratory skills and organizational abilities quickly made it a significant force in Bavarian politics.",

            "Munich Putsch and Nazi Party, 1923–28": "The failed putsch taught Hitler that he needed to gain power legally, leading to his strategy of using democratic means to destroy democracy.",

            "Growth in Support for the Nazis, 1929–32": "The economic crisis created the perfect conditions for extremist parties, as desperate people looked for radical solutions to their problems.",

            "How Hitler Became Chancellor, 1932–33": "Conservative politicians fatally underestimated Hitler, believing they could use him for their own purposes while keeping him under control.",

            "Creation of a Dictatorship, 1933–34": "Hitler's consolidation of power was remarkably swift, showing how quickly democratic institutions can be dismantled by determined authoritarians.",

            "The Police State": "The Nazi police state was characterized by overlapping agencies that created an atmosphere of fear and mutual surveillance among the population.",

            "Controlling and Influencing Attitudes": "Nazi propaganda was sophisticated and effective, using modern techniques to manipulate public opinion and create a cult of personality around Hitler.",

            "Opposition, Resistance, and Conformity": "The limited resistance to Nazi rule highlights how difficult it is to oppose a totalitarian regime once it has consolidated power.",

            "Nazi Policies Towards Women": "Nazi gender policies represented a dramatic reversal of women's progress during the Weimar period, showing how authoritarian regimes often target women's rights.",

            "Nazi Policies Towards the Young": "The Nazi focus on youth indoctrination was crucial to their long-term plans, as they sought to create a generation loyal only to Nazi ideology.",

            "Employment and Living Standards": "While the Nazis reduced unemployment, this came at the cost of workers' rights and was largely driven by preparation for war.",

            "Persecution of Minorities": "The systematic persecution of minorities was central to Nazi ideology and would escalate into the Holocaust during World War II."
        };

        return additionalContexts[question.subtopic] || "This event was part of the broader transformation of German society during this turbulent period.";
    }

    nextQuestion() {
        // Clear auto-progress timer
        this.clearAutoProgress();

        this.currentQuestionIndex++;

        if (this.currentQuestionIndex >= this.questions.length) {
            this.showResults();
        } else {
            this.displayQuestion();
        }
    }

    showResults() {
        const percentage = Math.round((this.score / this.questions.length) * 100);

        document.getElementById('final-score-text').textContent =
            `Your Score: ${this.score}/${this.questions.length}`;
        document.getElementById('score-percentage').textContent = `${percentage}%`;

        // Performance message
        let message = '';
        if (percentage >= 90) {
            message = 'Excellent! You have mastered this topic.';
        } else if (percentage >= 70) {
            message = 'Good job! You have a solid understanding.';
        } else if (percentage >= 50) {
            message = 'Not bad, but there\'s room for improvement.';
        } else {
            message = 'Keep studying! Review the material and try again.';
        }

        document.getElementById('performance-message').textContent = message;

        // Save quiz results if user is logged in
        if (this.currentUser) {
            this.saveQuizResults(percentage);
        }

        this.showScreen('results-screen');
    }

    saveQuizResults(percentage) {
        // Update user stats
        this.currentUser.stats.totalQuizzes++;
        this.currentUser.stats.totalQuestions += this.questions.length;
        this.currentUser.stats.totalCorrect += this.score;

        if (percentage > this.currentUser.stats.bestScore) {
            this.currentUser.stats.bestScore = percentage;
        }

        // Update subject-specific stats
        if (this.currentSubject === 'history') {
            this.currentUser.stats.historyQuizzes++;
        } else if (this.currentSubject === 'spanish') {
            this.currentUser.stats.spanishQuizzes++;
        } else if (this.currentSubject === 'chemistry') {
            this.currentUser.stats.chemistryQuizzes++;
        }

        // Add to quiz history
        const quizResult = {
            date: new Date().toISOString(),
            subject: this.currentSubject,
            topic: this.currentTopic,
            score: this.score,
            total: this.questions.length,
            percentage: percentage
        };

        this.currentUser.stats.quizHistory.unshift(quizResult);

        // Keep only last 10 quiz results
        if (this.currentUser.stats.quizHistory.length > 10) {
            this.currentUser.stats.quizHistory = this.currentUser.stats.quizHistory.slice(0, 10);
        }

        // Update localStorage
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

        // Update users array
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userIndex = users.findIndex(u => u.username === this.currentUser.username);
        if (userIndex !== -1) {
            users[userIndex] = this.currentUser;
            localStorage.setItem('users', JSON.stringify(users));
        }
    }

    showProfile() {
        this.hideUserDropdown();
        this.updateProfileDisplay();
        this.showScreen('profile-screen');
    }

    updateProfileDisplay() {
        if (!this.currentUser) return;

        // Update header with username
        document.getElementById('profile-username-header').textContent = this.currentUser.username;

        // Stats
        const stats = this.currentUser.stats;
        document.getElementById('total-quizzes').textContent = stats.totalQuizzes;
        document.getElementById('best-score').textContent = `${stats.bestScore}%`;

        const averageScore = stats.totalQuestions > 0 ?
            Math.round((stats.totalCorrect / stats.totalQuestions) * 100) : 0;
        document.getElementById('average-score').textContent = `${averageScore}%`;

        // Subject progress
        const historyProgress = stats.historyQuizzes > 0 ? Math.min(stats.historyQuizzes * 10, 100) : 0;
        const spanishProgress = stats.spanishQuizzes > 0 ? Math.min(stats.spanishQuizzes * 10, 100) : 0;
        const chemistryProgress = stats.chemistryQuizzes > 0 ? Math.min(stats.chemistryQuizzes * 10, 100) : 0;

        document.getElementById('history-progress').style.width = `${historyProgress}%`;
        document.getElementById('history-progress-text').textContent = `${historyProgress}%`;
        document.getElementById('spanish-progress').style.width = `${spanishProgress}%`;
        document.getElementById('spanish-progress-text').textContent = `${spanishProgress}%`;
        document.getElementById('chemistry-progress').style.width = `${chemistryProgress}%`;
        document.getElementById('chemistry-progress-text').textContent = `${chemistryProgress}%`;

        // Quiz history
        this.displayQuizHistory();
    }

    displayQuizHistory() {
        const container = document.getElementById('recent-quizzes');
        const history = this.currentUser.stats.quizHistory;

        if (history.length === 0) {
            container.innerHTML = '<p class="no-data">No recent activity</p>';
            return;
        }

        // Show only the last 3 quizzes for clean UI
        const recentHistory = history.slice(-3);

        container.innerHTML = recentHistory.map(quiz => {
            const date = new Date(quiz.date).toLocaleDateString();
            let subjectName;
            if (quiz.subject === 'history') {
                subjectName = 'Edexcel History';
            } else if (quiz.subject === 'spanish') {
                subjectName = 'AQA Spanish';
            } else if (quiz.subject === 'chemistry') {
                subjectName = 'AQA Chemistry';
            }

            return `
                <div class="quiz-history-item">
                    <div class="quiz-info">
                        <div class="quiz-subject">${subjectName}</div>
                        <div class="quiz-date">${date}</div>
                    </div>
                    <div class="quiz-score">${quiz.percentage}%</div>
                </div>
            `;
        }).join('');
    }

    getScoreClass(percentage) {
        if (percentage >= 90) return 'excellent';
        if (percentage >= 70) return 'good';
        if (percentage >= 50) return 'average';
        return 'poor';
    }

    getTopicDisplayName(topic) {
        const topicNames = {
            'keyTopic1': 'The Weimar Republic',
            'keyTopic2': 'Hitler\'s Rise to Power',
            'keyTopic3': 'Nazi Control and Dictatorship',
            'keyTopic4': 'Life in Nazi Germany',
            'all': 'All Edexcel History',
            'adjectives': 'Adjectives',
            'adverbs': 'Adverbs',
            'nouns': 'Nouns',
            'verbs': 'Verbs',
            'spanish-all': 'All AQA Spanish'
        };

        return topicNames[topic] || topic;
    }

    clearUserData() {
        if (confirm('Are you sure you want to clear all your quiz data? This cannot be undone.')) {
            this.currentUser.stats = {
                totalQuizzes: 0,
                totalQuestions: 0,
                totalCorrect: 0,
                historyQuizzes: 0,
                spanishQuizzes: 0,
                chemistryQuizzes: 0,
                bestScore: 0,
                quizHistory: []
            };

            // Update localStorage
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

            // Update users array
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.username === this.currentUser.username);
            if (userIndex !== -1) {
                users[userIndex] = this.currentUser;
                localStorage.setItem('users', JSON.stringify(users));
            }

            this.updateProfileDisplay();
            this.showMessage('All quiz data cleared!', 'success');
        }
    }

    showSettings() {
        this.hideUserDropdown();
        this.updateSettingsDisplay();
        this.showScreen('settings-screen');
    }

    updateSettingsDisplay() {
        // Get current auto-skip setting
        let autoSkipEnabled = true; // Default value

        if (this.currentUser) {
            // Ensure user has settings object (for existing users)
            if (!this.currentUser.settings) {
                this.currentUser.settings = {
                    autoSkipEnabled: true
                };
            }
            autoSkipEnabled = this.currentUser.settings.autoSkipEnabled;
        } else {
            // For non-logged-in users, check localStorage for guest settings
            const guestSettings = localStorage.getItem('guestSettings');
            if (guestSettings) {
                const settings = JSON.parse(guestSettings);
                autoSkipEnabled = settings.autoSkipEnabled;
            }
        }

        // Update the toggle switch
        const autoSkipToggle = document.getElementById('auto-skip-toggle');
        autoSkipToggle.checked = autoSkipEnabled;
    }

    saveSettings() {
        // Get the toggle value
        const autoSkipToggle = document.getElementById('auto-skip-toggle');
        const autoSkipEnabled = autoSkipToggle.checked;

        if (this.currentUser) {
            // Save settings for logged-in user
            // Ensure user has settings object
            if (!this.currentUser.settings) {
                this.currentUser.settings = {};
            }

            this.currentUser.settings.autoSkipEnabled = autoSkipEnabled;

            // Update localStorage
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

            // Update users array
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.username === this.currentUser.username);
            if (userIndex !== -1) {
                users[userIndex] = this.currentUser;
                localStorage.setItem('users', JSON.stringify(users));
            }
        } else {
            // Save settings for guest user
            const guestSettings = {
                autoSkipEnabled: autoSkipEnabled
            };
            localStorage.setItem('guestSettings', JSON.stringify(guestSettings));
        }

        this.showMessage('Settings saved successfully!', 'success');
        this.showSubjectScreen();
    }

    isAutoSkipEnabled() {
        if (this.currentUser && this.currentUser.settings) {
            return this.currentUser.settings.autoSkipEnabled;
        } else {
            // Check guest settings for non-logged-in users
            const guestSettings = localStorage.getItem('guestSettings');
            if (guestSettings) {
                const settings = JSON.parse(guestSettings);
                return settings.autoSkipEnabled;
            }
            return true; // Default to enabled
        }
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    resetQuiz() {
        this.currentTopic = null;
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.selectedAnswer = null;
        this.questionAnswered = false;
    }

    getExplanationDatabase() {
        return [
            {
                subtopic: "Origins of the Republic, 1918–19",
                keywords: ["kaiser", "wilhelm", "abdicated", "weimar", "constitution", "article 48"],
                explanation: "The Weimar Republic was established after Kaiser Wilhelm II abdicated in November 1918 following Germany's defeat in WWI. The new democratic constitution included Article 48, which allowed the president to rule by decree in emergencies - a provision that would later be exploited to undermine democracy."
            },
            {
                subtopic: "Early Challenges to the Weimar Republic, 1919–23",
                keywords: ["spartacist", "rosa luxemburg", "kapp putsch", "versailles", "hyperinflation", "ruhr"],
                explanation: "The early Weimar Republic faced severe challenges from both left and right. The Spartacist Uprising (1919) led by Rosa Luxemburg was crushed by the Freikorps, while the Kapp Putsch (1920) failed due to a general strike. The Treaty of Versailles created resentment, and hyperinflation in 1923 devastated the economy when France occupied the Ruhr."
            },
            {
                subtopic: "Golden Years: Recovery of the Republic, 1924–29",
                keywords: ["stresemann", "rentenmark", "dawes plan", "young plan", "locarno", "league of nations"],
                explanation: "Gustav Stresemann was key to Germany's recovery. He introduced the Rentenmark in 1923 to end hyperinflation, negotiated the Dawes Plan (1924) and Young Plan (1929) to restructure reparations, and improved international relations through the Locarno Pact (1925) and Germany's entry into the League of Nations (1926)."
            },
            {
                subtopic: "Changes in Society, 1924–29",
                keywords: ["women", "culture", "bauhaus", "cinema", "standard of living"],
                explanation: "The 'Golden Years' saw improvements in living standards, women's rights (including voting), and a flourishing of culture. The Bauhaus movement revolutionized architecture and design, while German cinema produced masterpieces like 'Metropolis' by Fritz Lang."
            },
            {
                subtopic: "Early Development of the Nazi Party, 1920–22",
                keywords: ["hitler", "german workers party", "nazi party", "twenty-five point", "sa", "stormtroopers"],
                explanation: "Hitler joined the German Workers' Party in 1919 and quickly became its leader, renaming it the Nazi Party. The Twenty-Five Point Programme outlined anti-Semitic and nationalist policies. The SA (Stormtroopers) were used for propaganda, intimidation, and protecting Nazi rallies."
            },
            {
                subtopic: "Munich Putsch and Nazi Party, 1923–28",
                keywords: ["munich putsch", "mein kampf", "prison", "bamberg conference"],
                explanation: "The Munich Putsch (1923) was Hitler's failed attempt to seize power in Bavaria. Though it failed and led to his arrest, it gave him national publicity. While in prison, he wrote 'Mein Kampf' outlining his racist ideology. The Bamberg Conference (1926) consolidated his control over the Nazi Party."
            },
            {
                subtopic: "Growth in Support for the Nazis, 1929–32",
                keywords: ["wall street crash", "unemployment", "communist", "propaganda", "charisma"],
                explanation: "The Wall Street Crash (1929) caused massive unemployment in Germany, which boosted support for extremist parties. The growth of the Communist Party scared the middle classes toward the Nazis. Hitler's charisma, effective propaganda, and the SA's street presence helped increase Nazi support dramatically."
            },
            {
                subtopic: "How Hitler Became Chancellor, 1932–33",
                keywords: ["chancellor", "hindenburg", "von papen", "january 1933", "largest party"],
                explanation: "In 1932 elections, the Nazis became the largest party but lacked a majority. President Hindenburg initially refused to appoint Hitler as Chancellor. However, political maneuvering by von Papen led to Hitler's appointment as Chancellor in January 1933, with the mistaken belief they could control him."
            },
            {
                subtopic: "Creation of a Dictatorship, 1933–34",
                keywords: ["reichstag fire", "enabling act", "night of long knives", "hindenburg death", "führer"],
                explanation: "Hitler rapidly consolidated power through key events: the Reichstag Fire (February 1933) was blamed on communists and led to emergency powers; the Enabling Act (March 1933) allowed Hitler to pass laws without parliament; the Night of the Long Knives (June 1934) eliminated SA leadership; and Hindenburg's death (August 1934) allowed Hitler to become Führer."
            },
            {
                subtopic: "The Police State",
                keywords: ["gestapo", "ss", "concentration camps", "people's court"],
                explanation: "The Nazi police state used multiple organizations to control the population: the Gestapo (secret police) monitored and arrested opponents; the SS ran concentration camps for political prisoners; and the legal system was corrupted with judges swearing loyalty to Hitler and the People's Court handling political trials."
            },
            {
                subtopic: "Controlling and Influencing Attitudes",
                keywords: ["goebbels", "propaganda", "nuremberg rallies", "olympics", "degenerate art"],
                explanation: "Joseph Goebbels' Propaganda Ministry controlled all media and organized spectacular events like the Nuremberg Rallies and the 1936 Berlin Olympics to showcase Nazi power. The regime promoted Nazi-approved art and architecture while banning 'degenerate' art that didn't fit their ideology."
            },
            {
                subtopic: "Opposition, Resistance, and Conformity",
                keywords: ["niemöller", "confessing church", "swing youth", "edelweiss pirates"],
                explanation: "While most Germans conformed due to fear, propaganda, and economic benefits, some resisted. Pastor Niemöller led the Confessing Church against Nazi religious policies. Youth groups like the Swing Youth and Edelweiss Pirates rejected Nazi ideology, but opposition had limited impact due to severe repression."
            },
            {
                subtopic: "Nazi Policies Towards Women",
                keywords: ["homemakers", "motherhood", "mother's cross", "traditional"],
                explanation: "Nazi ideology promoted women as homemakers focused on 'Kinder, Küche, Kirche' (children, kitchen, church). Policies included the Mother's Cross for large families, restrictions on women's employment, and promotion of traditional appearance. This reversed many advances women had made during the Weimar period."
            },
            {
                subtopic: "Nazi Policies Towards the Young",
                keywords: ["hitler youth", "league of german maidens", "education", "indoctrination"],
                explanation: "The Nazis targeted youth through the Hitler Youth (boys) and League of German Maidens (girls), which indoctrinated loyalty and promoted physical fitness. Education was transformed with Nazi curriculum, anti-Semitic teachings, and teachers required to be loyal to the regime."
            },
            {
                subtopic: "Employment and Living Standards",
                keywords: ["unemployment", "autobahns", "rearmament", "strength through joy", "labour front"],
                explanation: "The Nazis reduced unemployment through public works (like autobahn construction), rearmament, and the National Labour Service. However, 'invisible unemployment' excluded Jews and women from statistics. Programs like 'Strength Through Joy' provided holidays, but wages remained stagnant and trade unions were replaced by the Labour Front."
            },
            {
                subtopic: "Persecution of Minorities",
                keywords: ["racial ideology", "jewish", "nuremberg laws", "kristallnacht", "sterilization"],
                explanation: "Nazi racial ideology targeted multiple groups: Slavs, Roma, homosexuals, and disabled people faced persecution including forced sterilization. Jewish persecution escalated from the 1933 shop boycotts, through the Nuremberg Laws (1935) that stripped citizenship, to Kristallnacht (1938) when synagogues and businesses were attacked."
            }
        ];
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
}

// Initialize the quiz app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new QuizApp();
});
