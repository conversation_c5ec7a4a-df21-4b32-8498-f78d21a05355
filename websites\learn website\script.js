// Quiz App JavaScript
class QuizApp {
    constructor() {
        this.currentSubject = 'history';
        this.currentTopic = null;
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.selectedAnswer = null;
        this.questionAnswered = false;
        this.autoProgressTimer = null;
        this.currentUser = null;

        this.initializeEventListeners();
        this.initializeAuth();
    }

    initializeAuth() {
        // Check if user is already logged in
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);

            // Ensure backward compatibility - add settings if they don't exist
            if (!this.currentUser.settings) {
                this.currentUser.settings = {
                    autoSkipEnabled: true
                };
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            }

            this.showUserInterface();
        } else {
            this.showAuthInterface();
        }
    }

    showUserInterface() {
        // Update dropdown username
        document.getElementById('dropdown-username').textContent = this.currentUser.username;
        this.showSubjectScreen();
    }

    showAuthInterface() {
        this.showSubjectScreen();
    }

    toggleUserDropdown() {
        const dropdown = document.getElementById('user-dropdown');
        if (dropdown.classList.contains('hidden')) {
            this.showUserDropdown();
        } else {
            this.hideUserDropdown();
        }
    }

    showUserDropdown() {
        document.getElementById('user-dropdown').classList.remove('hidden');
    }

    hideUserDropdown() {
        document.getElementById('user-dropdown').classList.add('hidden');
    }

    initializeEventListeners() {
        // Subject tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const subject = e.currentTarget.dataset.subject;
                this.switchSubject(subject);
            });
        });

        // Topic selection
        document.querySelectorAll('.topic-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const topic = e.currentTarget.dataset.topic;
                this.startQuiz(topic);
            });
        });

        // Quiz controls
        document.getElementById('next-btn').addEventListener('click', () => {
            // Clear auto-progress if user manually clicks
            this.clearAutoProgress();
            this.nextQuestion();
        });

        document.getElementById('back-to-topics').addEventListener('click', () => {
            this.showSubjectScreen();
            this.resetQuiz();
        });

        // Results actions
        document.getElementById('retake-quiz').addEventListener('click', () => {
            this.startQuiz(this.currentTopic);
        });

        document.getElementById('new-topic').addEventListener('click', () => {
            this.showSubjectScreen();
            this.resetQuiz();
        });

        // Authentication event listeners
        document.getElementById('user-icon').addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.currentUser) {
                // If logged in, toggle dropdown
                this.toggleUserDropdown();
            } else {
                // If not logged in, show login modal
                this.showModal('login');
            }
        });

        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        document.getElementById('profile-btn').addEventListener('click', () => {
            this.showProfile();
        });

        document.getElementById('settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        // Modal event listeners
        document.querySelector('.close').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('show-register').addEventListener('click', (e) => {
            e.preventDefault();
            this.switchAuthForm('register');
        });

        document.getElementById('show-login').addEventListener('click', (e) => {
            e.preventDefault();
            this.switchAuthForm('login');
        });

        // Form submissions
        document.getElementById('login-form-element').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('register-form-element').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // Profile actions
        document.getElementById('back-from-profile').addEventListener('click', () => {
            this.showSubjectScreen();
        });

        document.getElementById('clear-data').addEventListener('click', () => {
            this.clearUserData();
        });

        // Settings actions
        document.getElementById('back-from-settings').addEventListener('click', () => {
            this.showSubjectScreen();
        });

        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('auth-modal');
            if (e.target === modal) {
                this.hideModal();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            const dropdown = document.getElementById('user-dropdown');
            const userIcon = document.getElementById('user-icon');

            if (!dropdown.contains(e.target) && !userIcon.contains(e.target)) {
                this.hideUserDropdown();
            }
        });
    }

    switchSubject(subject) {
        this.currentSubject = subject;

        // Update tab styling
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-subject="${subject}"]`).classList.add('active');

        this.showSubjectScreen();
    }

    showSubjectScreen() {
        if (this.currentSubject === 'history') {
            this.showScreen('history-topic-selection');
        } else {
            this.showScreen('spanish-topic-selection');
        }
    }

    showModal(type) {
        document.getElementById('auth-modal').style.display = 'block';
        if (type === 'login') {
            this.switchAuthForm('login');
        } else {
            this.switchAuthForm('register');
        }
    }

    hideModal() {
        document.getElementById('auth-modal').style.display = 'none';
        this.clearAuthForms();
    }

    switchAuthForm(type) {
        if (type === 'login') {
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('register-form').classList.add('hidden');
        } else {
            document.getElementById('login-form').classList.add('hidden');
            document.getElementById('register-form').classList.remove('hidden');
        }
    }

    clearAuthForms() {
        document.getElementById('login-form-element').reset();
        document.getElementById('register-form-element').reset();
    }

    handleLogin() {
        const username = document.getElementById('login-username').value;
        const password = document.getElementById('login-password').value;

        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.username === username && u.password === password);

        if (user) {
            this.currentUser = user;
            localStorage.setItem('currentUser', JSON.stringify(user));
            this.showUserInterface();
            this.hideModal();
            this.showMessage('Login successful!', 'success');
        } else {
            this.showMessage('Invalid username or password!', 'error');
        }
    }

    handleRegister() {
        const username = document.getElementById('register-username').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;

        if (password !== confirmPassword) {
            this.showMessage('Passwords do not match!', 'error');
            return;
        }

        // Get existing users
        const users = JSON.parse(localStorage.getItem('users') || '[]');

        // Check if username already exists
        if (users.find(u => u.username === username)) {
            this.showMessage('Username already exists!', 'error');
            return;
        }

        // Create new user
        const newUser = {
            username,
            email,
            password,
            joinDate: new Date().toISOString(),
            settings: {
                autoSkipEnabled: true
            },
            stats: {
                totalQuizzes: 0,
                totalQuestions: 0,
                totalCorrect: 0,
                historyQuizzes: 0,
                spanishQuizzes: 0,
                bestScore: 0,
                quizHistory: []
            }
        };

        users.push(newUser);
        localStorage.setItem('users', JSON.stringify(users));

        this.currentUser = newUser;
        localStorage.setItem('currentUser', JSON.stringify(newUser));

        this.showUserInterface();
        this.hideModal();
        this.showMessage('Registration successful!', 'success');
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        this.hideUserDropdown();
        this.showAuthInterface();
        this.showMessage('Logged out successfully!', 'success');
    }

    showMessage(message, type) {
        // Create a temporary message element
        const messageEl = document.createElement('div');
        messageEl.className = `notification ${type}`;

        // Add icon based on type
        const icon = document.createElement('span');
        icon.innerHTML = this.getNotificationIcon(type);

        // Add message text
        const messageText = document.createElement('span');
        messageText.textContent = message;

        messageEl.appendChild(icon);
        messageEl.appendChild(messageText);

        document.body.appendChild(messageEl);

        // Auto-remove after 3 seconds with fade-out animation
        setTimeout(() => {
            messageEl.classList.add('fade-out');
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 300); // Wait for fade-out animation to complete
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>',
            error: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>',
            warning: '<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>'
        };
        return icons[type] || icons.success;
    }

    startQuiz(topic) {
        this.currentTopic = topic;

        if (this.currentSubject === 'history') {
            this.questions = this.generateHistoryQuestions(topic);
        } else {
            this.questions = this.generateSpanishQuestions(topic);
        }

        this.currentQuestionIndex = 0;
        this.score = 0;
        this.showScreen('quiz-screen');
        this.displayQuestion();
    }

    generateHistoryQuestions(topic) {
        // Get all available predefined questions
        const allTrueFalseQuestions = this.getTrueFalseStatements();
        const allMultipleChoiceQuestions = this.getMultipleChoiceQuestions();

        let availableQuestions = [];

        if (topic === 'all') {
            // Use all questions
            availableQuestions = [...allTrueFalseQuestions, ...allMultipleChoiceQuestions];
        } else {
            // Filter questions for specific topic
            const topicData = contentData[topic];
            availableQuestions = [
                ...allTrueFalseQuestions.filter(q => q.topic === topicData.title),
                ...allMultipleChoiceQuestions.filter(q => q.topic === topicData.title)
            ];
        }

        // Shuffle and select 15 unique questions
        const shuffledQuestions = this.shuffleArray(availableQuestions);
        const selectedQuestions = shuffledQuestions.slice(0, Math.min(15, shuffledQuestions.length));

        // Convert to quiz format
        const questions = selectedQuestions.map(q => {
            if (q.type === 'true-false') {
                return {
                    type: 'true-false',
                    question: `True or False: ${q.statement}`,
                    answers: ['True', 'False'],
                    correctAnswer: q.isTrue ? 'True' : 'False',
                    subtopic: q.subtopic,
                    id: q.id // Add unique ID to prevent duplicates
                };
            } else {
                return {
                    type: 'multiple-choice',
                    question: q.question,
                    answers: this.shuffleArray([q.correctAnswer, ...q.distractors]),
                    correctAnswer: q.correctAnswer,
                    subtopic: q.subtopic,
                    id: q.id
                };
            }
        });

        return questions;
    }

    generateSpanishQuestions(category) {
        let vocabularyToUse = [];

        if (category === 'spanish-all') {
            // Use all Spanish vocabulary
            vocabularyToUse = [
                ...spanishVocabulary.adjectives,
                ...spanishVocabulary.adverbs,
                ...spanishVocabulary.nouns,
                ...spanishVocabulary.verbs
            ];
        } else {
            // Use specific category
            vocabularyToUse = spanishVocabulary[category] || [];
        }

        // Generate 15 questions
        const questions = [];
        const usedWords = new Set();

        for (let i = 0; i < 15 && vocabularyToUse.length > 0; i++) {
            let randomWord;
            let attempts = 0;

            // Find a word we haven't used yet
            do {
                randomWord = vocabularyToUse[Math.floor(Math.random() * vocabularyToUse.length)];
                attempts++;
            } while (usedWords.has(randomWord.spanish) && attempts < 50);

            if (attempts >= 50) break; // Prevent infinite loop

            usedWords.add(randomWord.spanish);

            // Randomly choose question type
            const questionType = Math.random() > 0.5 ? 'multiple-choice' : 'true-false';

            if (questionType === 'multiple-choice') {
                questions.push(this.generateSpanishMultipleChoice(randomWord, vocabularyToUse, category));
            } else {
                questions.push(this.generateSpanishTrueFalse(randomWord, vocabularyToUse, category));
            }
        }

        return this.shuffleArray(questions);
    }

    generateSpanishMultipleChoice(word, allWords, category) {
        // Create distractors from the same category
        const otherWords = allWords.filter(w => w.spanish !== word.spanish);
        const distractors = this.shuffleArray(otherWords)
            .slice(0, 3)
            .map(w => w.english);

        return {
            type: 'multiple-choice',
            question: `What does "${word.spanish}" mean in English?`,
            answers: this.shuffleArray([word.english, ...distractors]),
            correctAnswer: word.english,
            subtopic: category,
            id: `spanish_mc_${word.spanish}`,
            spanishWord: word.spanish,
            actualMeaning: word.english
        };
    }

    generateSpanishTrueFalse(word, allWords, category) {
        const isTrue = Math.random() > 0.5;

        if (isTrue) {
            return {
                type: 'true-false',
                question: `True or False: "${word.spanish}" means "${word.english}" in English`,
                answers: ['True', 'False'],
                correctAnswer: 'True',
                subtopic: category,
                id: `spanish_tf_true_${word.spanish}`,
                spanishWord: word.spanish,
                actualMeaning: word.english
            };
        } else {
            // Create a false statement with a different meaning
            const otherWords = allWords.filter(w => w.spanish !== word.spanish);
            const wrongMeaning = otherWords[Math.floor(Math.random() * otherWords.length)].english;

            return {
                type: 'true-false',
                question: `True or False: "${word.spanish}" means "${wrongMeaning}" in English`,
                answers: ['True', 'False'],
                correctAnswer: 'False',
                subtopic: category,
                id: `spanish_tf_false_${word.spanish}`,
                spanishWord: word.spanish,
                actualMeaning: word.english,
                wrongMeaning: wrongMeaning
            };
        }
    }



    getMultipleChoiceQuestions() {
        return [
            // Weimar Republic questions
            {
                id: "mc_1",
                type: "multiple-choice",
                question: "On what specific date did Kaiser Wilhelm II abdicate?",
                correctAnswer: "9 November 1918",
                distractors: ["11 November 1918", "8 November 1918", "10 November 1918"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "mc_2",
                type: "multiple-choice",
                question: "Who drafted the Weimar Constitution?",
                correctAnswer: "Hugo Preuss",
                distractors: ["Gustav Stresemann", "Matthias Erzberger", "Friedrich Ebert"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "mc_3",
                type: "multiple-choice",
                question: "How much were the reparations imposed by the Treaty of Versailles?",
                correctAnswer: "£6.6 billion",
                distractors: ["£4.2 billion", "£8.8 billion", "£5.5 billion"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_4",
                type: "multiple-choice",
                question: "What was the exchange rate at the peak of hyperinflation in November 1923?",
                correctAnswer: "1 USD = 4.2 trillion marks",
                distractors: ["1 USD = 2.1 trillion marks", "1 USD = 6.8 trillion marks", "1 USD = 3.5 trillion marks"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_5",
                type: "multiple-choice",
                question: "How many Freikorps participated in the Kapp Putsch?",
                correctAnswer: "5,000",
                distractors: ["3,000", "7,000", "10,000"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "mc_6",
                type: "multiple-choice",
                question: "On what date was the Rentenmark introduced?",
                correctAnswer: "20 November 1923",
                distractors: ["15 November 1923", "25 November 1923", "10 November 1923"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "mc_7",
                type: "multiple-choice",
                question: "How much did the Young Plan reduce reparations to?",
                correctAnswer: "£2 billion",
                distractors: ["£3 billion", "£1.5 billion", "£2.5 billion"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "mc_8",
                type: "multiple-choice",
                question: "What percentage of women voted in the first Weimar election?",
                correctAnswer: "90%",
                distractors: ["85%", "95%", "80%"],
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Changes in Society, 1924–29"
            },
            // Hitler's Rise to Power questions
            {
                id: "mc_9",
                type: "multiple-choice",
                question: "When was the NSDAP (Nazi Party) officially renamed from the German Workers' Party?",
                correctAnswer: "February 1920",
                distractors: ["January 1920", "March 1920", "April 1920"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "mc_10",
                type: "multiple-choice",
                question: "How many Nazis were killed during the Munich Putsch?",
                correctAnswer: "16",
                distractors: ["12", "20", "18"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "mc_11",
                type: "multiple-choice",
                question: "How many people were unemployed in Germany by 1932?",
                correctAnswer: "6 million",
                distractors: ["4 million", "8 million", "5 million"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Growth in Support for the Nazis, 1929–32"
            },
            {
                id: "mc_12",
                type: "multiple-choice",
                question: "How many seats did the Nazis win in July 1932?",
                correctAnswer: "230 seats",
                distractors: ["196 seats", "250 seats", "210 seats"],
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "How Hitler Became Chancellor, 1932–33"
            },
            // Nazi Control questions
            {
                id: "mc_13",
                type: "multiple-choice",
                question: "Who was blamed for the Reichstag Fire?",
                correctAnswer: "Marinus van der Lubbe",
                distractors: ["Karl Liebknecht", "Rosa Luxemburg", "Ernst Thälmann"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "mc_14",
                type: "multiple-choice",
                question: "How many people were killed during the Night of the Long Knives?",
                correctAnswer: "Over 400",
                distractors: ["Over 200", "Over 600", "Over 300"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "mc_15",
                type: "multiple-choice",
                question: "Who led the Gestapo?",
                correctAnswer: "Reinhard Heydrich",
                distractors: ["Heinrich Himmler", "Roland Freisler", "Rudolf Hess"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "The Police State"
            },
            {
                id: "mc_16",
                type: "multiple-choice",
                question: "How much did the Volksempfänger radio cost?",
                correctAnswer: "76 Reichsmarks",
                distractors: ["65 Reichsmarks", "85 Reichsmarks", "70 Reichsmarks"],
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            // Life in Nazi Germany questions
            {
                id: "mc_17",
                type: "multiple-choice",
                question: "How much was the marriage loan under the Law for Encouragement of Marriage?",
                correctAnswer: "1,000 Reichsmarks",
                distractors: ["800 Reichsmarks", "1,200 Reichsmarks", "750 Reichsmarks"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards Women"
            },
            {
                id: "mc_18",
                type: "multiple-choice",
                question: "From what year was Hitler Youth membership compulsory?",
                correctAnswer: "1936",
                distractors: ["1933", "1935", "1937"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards the Young"
            },
            {
                id: "mc_19",
                type: "multiple-choice",
                question: "How much did unemployment fall from 1929 to 1939?",
                correctAnswer: "From 6.1 million to 0.5 million",
                distractors: ["From 5.5 million to 0.3 million", "From 7.2 million to 0.8 million", "From 6.8 million to 0.6 million"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Employment and Living Standards"
            },
            {
                id: "mc_20",
                type: "multiple-choice",
                question: "How many synagogues were destroyed during Kristallnacht?",
                correctAnswer: "206",
                distractors: ["150", "250", "180"],
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            }
        ];
    }

    getTrueFalseStatements() {
        return [
            // Weimar Republic statements
            {
                id: "tf_1",
                type: "true-false",
                statement: "Kaiser Wilhelm II abdicated on 9 November 1918",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "tf_2",
                type: "true-false",
                statement: "Hugo Preuss drafted the Weimar Constitution",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Origins of the Republic, 1918–19"
            },
            {
                id: "tf_3",
                type: "true-false",
                statement: "The Treaty of Versailles imposed £6.6 billion in reparations",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_4",
                type: "true-false",
                statement: "At the peak of hyperinflation, 1 USD equaled 4.2 trillion marks",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_4",
                type: "true-false",
                statement: "The Spartacist Uprising was led by Rosa Luxemburg in 1919",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_5",
                type: "true-false",
                statement: "The Kapp Putsch was successful in overthrowing the government",
                isTrue: false,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_6",
                type: "true-false",
                statement: "The French occupied the Ruhr in 1923",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Early Challenges to the Weimar Republic, 1919–23"
            },
            {
                id: "tf_7",
                type: "true-false",
                statement: "Gustav Stresemann introduced the Rentenmark in 1924",
                isTrue: false,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },
            {
                id: "tf_8",
                type: "true-false",
                statement: "Germany joined the League of Nations in 1926",
                isTrue: true,
                topic: "The Weimar Republic, 1918–29",
                subtopic: "Golden Years: Recovery of the Republic, 1924–29"
            },

            // Hitler's Rise to Power statements
            {
                id: "tf_9",
                type: "true-false",
                statement: "Hitler joined the German Workers' Party in 1919",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "tf_10",
                type: "true-false",
                statement: "The SA were also known as Brownshirts",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Early Development of the Nazi Party, 1920–22"
            },
            {
                id: "tf_11",
                type: "true-false",
                statement: "The Munich Putsch was successful in 1923",
                isTrue: false,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "tf_12",
                type: "true-false",
                statement: "Hitler wrote Mein Kampf while in prison",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Munich Putsch and Nazi Party, 1923–28"
            },
            {
                id: "tf_13",
                type: "true-false",
                statement: "The Wall Street Crash occurred in 1929",
                isTrue: true,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "Growth in Support for the Nazis, 1929–32"
            },
            {
                id: "tf_14",
                type: "true-false",
                statement: "Hitler became Chancellor in January 1934",
                isTrue: false,
                topic: "Hitler's Rise to Power, 1919–33",
                subtopic: "How Hitler Became Chancellor, 1932–33"
            },

            // Nazi Control statements
            {
                id: "tf_15",
                type: "true-false",
                statement: "The Reichstag Fire occurred in February 1933",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_16",
                type: "true-false",
                statement: "The Enabling Act was passed in March 1933",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_17",
                type: "true-false",
                statement: "The Night of the Long Knives occurred in 1935",
                isTrue: false,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Creation of a Dictatorship, 1933–34"
            },
            {
                id: "tf_18",
                type: "true-false",
                statement: "The Gestapo was the Nazi secret police",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "The Police State"
            },
            {
                id: "tf_19",
                type: "true-false",
                statement: "Joseph Goebbels was in charge of propaganda",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            {
                id: "tf_20",
                type: "true-false",
                statement: "The Volksempfänger radio cost 76 Reichsmarks",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },
            {
                id: "tf_21",
                type: "true-false",
                statement: "70% of German households owned a radio by 1939",
                isTrue: true,
                topic: "Nazi Control and Dictatorship, 1933–39",
                subtopic: "Controlling and Influencing Attitudes"
            },

            // Life in Nazi Germany statements
            {
                id: "tf_22",
                type: "true-false",
                statement: "The Law for Encouragement of Marriage provided 1,000 Reichsmark loans",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards Women"
            },
            {
                id: "tf_23",
                type: "true-false",
                statement: "Hitler Youth membership became compulsory in 1936",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Nazi Policies Towards the Young"
            },
            {
                id: "tf_24",
                type: "true-false",
                statement: "Unemployment fell from 6.1 million in 1929 to 0.5 million in 1939",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Employment and Living Standards"
            },
            {
                id: "tf_25",
                type: "true-false",
                statement: "206 synagogues were destroyed during Kristallnacht",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            },
            {
                id: "tf_26",
                type: "true-false",
                statement: "8,000 homosexuals were imprisoned by 1938",
                isTrue: true,
                topic: "Life in Nazi Germany, 1933–39",
                subtopic: "Persecution of Minorities"
            }
        ];
    }

    generateDistractors(correctContent, subtopic) {
        // Generate plausible but incorrect answers
        const distractors = [
            "This event occurred during the French Revolution",
            "This was primarily a British policy initiative",
            "This happened after World War II ended",
            "This was a result of American intervention",
            "This occurred during the Industrial Revolution"
        ];
        
        return this.shuffleArray(distractors).slice(0, 3);
    }

    displayQuestion() {
        const question = this.questions[this.currentQuestionIndex];

        // Clear any existing explanation
        const existingExplanation = document.querySelector('.explanation');
        if (existingExplanation) {
            existingExplanation.remove();
        }

        // Clear any auto-progress timer
        this.clearAutoProgress();

        // Update progress
        const progress = ((this.currentQuestionIndex + 1) / this.questions.length) * 100;
        document.querySelector('.progress-fill').style.width = `${progress}%`;

        // Update counters
        document.getElementById('question-counter').textContent =
            `Question ${this.currentQuestionIndex + 1} of ${this.questions.length}`;
        document.getElementById('score').textContent =
            `Score: ${this.score}/${this.questions.length}`;

        // Display question
        document.getElementById('question-text').textContent = question.question;
        document.getElementById('question-type').textContent =
            question.type === 'multiple-choice' ? 'Multiple Choice' : 'True/False';

        // Display answers
        const answersContainer = document.getElementById('answers-container');
        answersContainer.innerHTML = '';

        question.answers.forEach((answer, index) => {
            const button = document.createElement('button');
            button.className = 'answer-option';
            button.textContent = answer;
            button.addEventListener('click', () => this.selectAnswer(answer, button));
            answersContainer.appendChild(button);
        });

        // Reset state
        this.selectedAnswer = null;
        this.questionAnswered = false;
        document.getElementById('next-btn').disabled = true;
        document.getElementById('next-btn').textContent = 'Next Question';
    }

    selectAnswer(answer, buttonElement) {
        if (this.questionAnswered) return;
        
        // Clear previous selections
        document.querySelectorAll('.answer-option').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        // Mark selected
        buttonElement.classList.add('selected');
        this.selectedAnswer = answer;
        
        // Check answer immediately
        this.checkAnswer();
    }

    checkAnswer() {
        const question = this.questions[this.currentQuestionIndex];
        const isCorrect = this.selectedAnswer === question.correctAnswer;

        if (isCorrect) {
            this.score++;
        }

        // Show correct/incorrect styling
        document.querySelectorAll('.answer-option').forEach(btn => {
            if (btn.textContent === question.correctAnswer) {
                btn.classList.add('correct');
            } else if (btn.textContent === this.selectedAnswer && !isCorrect) {
                btn.classList.add('incorrect');
            }
            btn.style.pointerEvents = 'none';
        });

        // Always show explanation for educational value
        this.showExplanation(question, isCorrect);

        this.questionAnswered = true;
        document.getElementById('next-btn').disabled = false;

        // Update score display
        document.getElementById('score').textContent =
            `Score: ${this.score}/${this.currentQuestionIndex + 1}`;

        // Auto-progress after 4 seconds if answer is correct and auto-skip is enabled
        if (isCorrect && this.isAutoSkipEnabled()) {
            this.startAutoProgress();
        }
    }

    startAutoProgress() {
        // Clear any existing timer
        if (this.autoProgressTimer) {
            clearTimeout(this.autoProgressTimer);
        }

        // Show countdown on next button
        this.showCountdown();

        // Set timer for auto-progression
        this.autoProgressTimer = setTimeout(() => {
            this.nextQuestion();
        }, 4000);
    }

    showCountdown() {
        const nextBtn = document.getElementById('next-btn');
        const originalText = nextBtn.textContent;
        let countdown = 4;

        const updateCountdown = () => {
            nextBtn.textContent = `Next Question (${countdown})`;
            countdown--;

            if (countdown >= 0) {
                setTimeout(updateCountdown, 1000);
            }
        };

        updateCountdown();
    }

    clearAutoProgress() {
        if (this.autoProgressTimer) {
            clearTimeout(this.autoProgressTimer);
            this.autoProgressTimer = null;
        }

        // Reset button text
        const nextBtn = document.getElementById('next-btn');
        nextBtn.textContent = 'Next Question';
    }

    showExplanation(question, isCorrect) {
        // Remove any existing explanation
        const existingExplanation = document.querySelector('.explanation');
        if (existingExplanation) {
            existingExplanation.remove();
        }

        const explanation = document.createElement('div');
        explanation.className = isCorrect ? 'explanation correct-explanation' : 'explanation incorrect-explanation';

        let explanationText = '';

        if (question.explanation) {
            explanationText = question.explanation;
        } else {
            // Generate explanation based on question content
            explanationText = this.generateExplanation(question, isCorrect);
        }

        const headerIcon = isCorrect ? '✅' : '📚';
        const headerText = isCorrect ? 'Excellent! Here\'s more context:' : 'Learn More:';

        explanation.innerHTML = `
            <div class="explanation-header">
                <strong>${headerIcon} ${headerText}</strong>
            </div>
            <div class="explanation-content">
                ${explanationText}
            </div>
        `;

        // Insert explanation after the answers container
        const answersContainer = document.getElementById('answers-container');
        answersContainer.parentNode.insertBefore(explanation, answersContainer.nextSibling);

        // Auto-scroll to ensure the explanation and buttons are visible
        this.scrollToExplanation(explanation);
    }

    scrollToExplanation(explanationElement) {
        // Wait for the explanation to be rendered and animations to complete
        setTimeout(() => {
            // Get the quiz controls (Next Question button area)
            const quizControls = document.querySelector('.quiz-controls');

            if (quizControls && explanationElement) {
                // Calculate the optimal scroll position
                const explanationRect = explanationElement.getBoundingClientRect();
                const controlsRect = quizControls.getBoundingClientRect();
                const viewportHeight = window.innerHeight;

                // Check if both explanation and controls are fully visible
                const explanationVisible = explanationRect.top >= 0 && explanationRect.bottom <= viewportHeight;
                const controlsVisible = controlsRect.top >= 0 && controlsRect.bottom <= viewportHeight;

                // Only scroll if either element is not fully visible
                if (!explanationVisible || !controlsVisible) {
                    // Calculate the ideal scroll position
                    // We want to show the explanation with some top padding
                    // and ensure the controls are visible at the bottom
                    // Adjust padding based on screen size
                    const isMobile = window.innerWidth <= 768;
                    const topPadding = isMobile ? 40 : 60;
                    const bottomPadding = isMobile ? 30 : 20;

                    // Calculate how much space we need
                    const neededHeight = explanationRect.height + controlsRect.height + topPadding + bottomPadding;

                    let targetScrollTop;

                    if (neededHeight <= viewportHeight) {
                        // If everything fits, position explanation with top padding
                        targetScrollTop = window.pageYOffset + explanationRect.top - topPadding;
                    } else {
                        // If content is too tall, prioritize showing the controls (buttons)
                        // Position so controls are visible at bottom with padding
                        targetScrollTop = window.pageYOffset + controlsRect.bottom - viewportHeight + bottomPadding;
                    }

                    // Ensure we don't scroll past the top of the page
                    targetScrollTop = Math.max(0, targetScrollTop);

                    // Smooth scroll to the calculated position
                    window.scrollTo({
                        top: targetScrollTop,
                        behavior: 'smooth'
                    });
                }
            }
        }, 150); // Slightly longer delay to ensure animations complete
    }

    generateExplanation(question, isCorrect) {
        if (this.currentSubject === 'spanish') {
            return this.generateSpanishExplanation(question, isCorrect);
        }

        // Get additional context based on the question's subtopic
        const explanations = this.getExplanationDatabase();

        // Find relevant explanation
        const relevantExplanation = explanations.find(exp =>
            exp.subtopic === question.subtopic ||
            exp.keywords.some(keyword => question.question.toLowerCase().includes(keyword.toLowerCase()))
        );

        let explanationText = '';

        if (isCorrect) {
            // For correct answers, focus on additional context and related information
            if (relevantExplanation) {
                explanationText = `<strong>Why this is correct:</strong> ${relevantExplanation.explanation}<br><br>
                                 <strong>Additional Context:</strong> ${this.getAdditionalContext(question, relevantExplanation)}`;
            } else {
                explanationText = `<strong>Great job!</strong> This relates to ${question.subtopic}. Your understanding of this historical period is developing well.`;
            }
        } else {
            // For incorrect answers, show correct answer and explanation
            if (relevantExplanation) {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Explanation:</strong> ${relevantExplanation.explanation}`;
            } else {
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Context:</strong> This relates to ${question.subtopic}. Review this topic for more details.`;
            }
        }

        return explanationText;
    }

    generateSpanishExplanation(question, isCorrect) {
        // Use stored values from question object when available, otherwise extract from question text
        const spanishWord = question.spanishWord || (question.question.match(/"([^"]+)"/)?.[0]?.replace(/"/g, '') || '');
        const actualMeaning = question.actualMeaning || this.findSpanishWordMeaning(spanishWord);

        let explanationText = '';

        if (isCorrect) {
            if (question.type === 'multiple-choice') {
                // For multiple choice, question.correctAnswer is the English meaning
                explanationText = `<strong>¡Excelente!</strong> You correctly identified the meaning.<br><br>
                                 <strong>Word:</strong> ${spanishWord}<br>
                                 <strong>Meaning:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
            } else {
                // True/False correct - always show actual meaning regardless of True/False answer
                if (question.correctAnswer === 'True') {
                    explanationText = `<strong>¡Correcto!</strong> You correctly identified this as true!<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Meaning:</strong> ${actualMeaning}<br><br>
                                     <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                } else {
                    // They correctly said it was false
                    const wrongMeaning = question.wrongMeaning || (question.question.match(/means "([^"]+)"/)?.[1] || '');

                    explanationText = `<strong>¡Correcto!</strong> You correctly identified this as false!<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Does NOT mean:</strong> ${wrongMeaning}<br>
                                     <strong>Actually means:</strong> ${actualMeaning}<br><br>
                                     <strong>Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                }
            }
        } else {
            if (question.type === 'multiple-choice') {
                // For multiple choice, question.correctAnswer is the English meaning
                explanationText = `<strong>Correct Answer:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Word:</strong> ${spanishWord}<br>
                                 <strong>Meaning:</strong> ${question.correctAnswer}<br><br>
                                 <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
            } else {
                // True/False incorrect - show the actual meaning
                if (question.correctAnswer === 'True') {
                    // They said false when it was true
                    explanationText = `<strong>Correct Answer:</strong> True<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Actual meaning:</strong> ${actualMeaning}<br><br>
                                     <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                } else {
                    // They said true when it was false
                    const wrongMeaning = question.wrongMeaning || (question.question.match(/means "([^"]+)"/)?.[1] || '');

                    explanationText = `<strong>Correct Answer:</strong> False<br><br>
                                     <strong>Word:</strong> ${spanishWord}<br>
                                     <strong>Does NOT mean:</strong> ${wrongMeaning}<br>
                                     <strong>Actually means:</strong> ${actualMeaning}<br><br>
                                     <strong>Study Tip:</strong> ${this.getSpanishTip(question.subtopic)}`;
                }
            }
        }

        return explanationText;
    }

    findSpanishWordMeaning(spanishWord) {
        // Search through all Spanish vocabulary categories to find the word
        const allCategories = ['adjectives', 'adverbs', 'nouns', 'verbs'];

        for (const category of allCategories) {
            const words = spanishVocabulary[category];
            const foundWord = words.find(word => word.spanish === spanishWord);
            if (foundWord) {
                return foundWord.english;
            }
        }

        return 'meaning not found';
    }

    getSpanishTip(category) {
        const tips = {
            'adjectives': 'Remember that Spanish adjectives usually come after the noun they describe, and they must agree in gender and number.',
            'adverbs': 'Many Spanish adverbs end in -mente (equivalent to -ly in English). Practice using them to describe how actions are performed.',
            'nouns': 'Spanish nouns have gender (masculine/feminine). Learning the gender with each noun will help with adjective agreement.',
            'verbs': 'Spanish verbs change their endings based on who is doing the action. Practice conjugating regular verbs first.',
            'spanish-all': 'Try to use new vocabulary in sentences to help remember the meanings. Context helps with retention!'
        };

        return tips[category] || 'Keep practicing! Regular review helps build vocabulary retention.';
    }

    getAdditionalContext(question, explanation) {
        // Provide additional related information for correct answers
        const additionalContexts = {
            "Origins of the Republic, 1918–19": "The Weimar Republic's democratic experiment was Germany's first attempt at parliamentary democracy, but it faced immediate challenges from both political extremes.",

            "Early Challenges to the Weimar Republic, 1919–23": "These early crises nearly destroyed the republic and created lasting resentment that extremist parties would later exploit.",

            "Golden Years: Recovery of the Republic, 1924–29": "This period of stability was largely dependent on American loans, making Germany vulnerable when the global economy collapsed in 1929.",

            "Changes in Society, 1924–29": "The cultural flowering of the Weimar period represented a brief moment of creative freedom that would be brutally suppressed under Nazi rule.",

            "Early Development of the Nazi Party, 1920–22": "The Nazi Party started as a small, radical group but Hitler's oratory skills and organizational abilities quickly made it a significant force in Bavarian politics.",

            "Munich Putsch and Nazi Party, 1923–28": "The failed putsch taught Hitler that he needed to gain power legally, leading to his strategy of using democratic means to destroy democracy.",

            "Growth in Support for the Nazis, 1929–32": "The economic crisis created the perfect conditions for extremist parties, as desperate people looked for radical solutions to their problems.",

            "How Hitler Became Chancellor, 1932–33": "Conservative politicians fatally underestimated Hitler, believing they could use him for their own purposes while keeping him under control.",

            "Creation of a Dictatorship, 1933–34": "Hitler's consolidation of power was remarkably swift, showing how quickly democratic institutions can be dismantled by determined authoritarians.",

            "The Police State": "The Nazi police state was characterized by overlapping agencies that created an atmosphere of fear and mutual surveillance among the population.",

            "Controlling and Influencing Attitudes": "Nazi propaganda was sophisticated and effective, using modern techniques to manipulate public opinion and create a cult of personality around Hitler.",

            "Opposition, Resistance, and Conformity": "The limited resistance to Nazi rule highlights how difficult it is to oppose a totalitarian regime once it has consolidated power.",

            "Nazi Policies Towards Women": "Nazi gender policies represented a dramatic reversal of women's progress during the Weimar period, showing how authoritarian regimes often target women's rights.",

            "Nazi Policies Towards the Young": "The Nazi focus on youth indoctrination was crucial to their long-term plans, as they sought to create a generation loyal only to Nazi ideology.",

            "Employment and Living Standards": "While the Nazis reduced unemployment, this came at the cost of workers' rights and was largely driven by preparation for war.",

            "Persecution of Minorities": "The systematic persecution of minorities was central to Nazi ideology and would escalate into the Holocaust during World War II."
        };

        return additionalContexts[question.subtopic] || "This event was part of the broader transformation of German society during this turbulent period.";
    }

    nextQuestion() {
        // Clear auto-progress timer
        this.clearAutoProgress();

        this.currentQuestionIndex++;

        if (this.currentQuestionIndex >= this.questions.length) {
            this.showResults();
        } else {
            this.displayQuestion();
        }
    }

    showResults() {
        const percentage = Math.round((this.score / this.questions.length) * 100);

        document.getElementById('final-score-text').textContent =
            `Your Score: ${this.score}/${this.questions.length}`;
        document.getElementById('score-percentage').textContent = `${percentage}%`;

        // Performance message
        let message = '';
        if (percentage >= 90) {
            message = 'Excellent! You have mastered this topic.';
        } else if (percentage >= 70) {
            message = 'Good job! You have a solid understanding.';
        } else if (percentage >= 50) {
            message = 'Not bad, but there\'s room for improvement.';
        } else {
            message = 'Keep studying! Review the material and try again.';
        }

        document.getElementById('performance-message').textContent = message;

        // Save quiz results if user is logged in
        if (this.currentUser) {
            this.saveQuizResults(percentage);
        }

        this.showScreen('results-screen');
    }

    saveQuizResults(percentage) {
        // Update user stats
        this.currentUser.stats.totalQuizzes++;
        this.currentUser.stats.totalQuestions += this.questions.length;
        this.currentUser.stats.totalCorrect += this.score;

        if (percentage > this.currentUser.stats.bestScore) {
            this.currentUser.stats.bestScore = percentage;
        }

        // Update subject-specific stats
        if (this.currentSubject === 'history') {
            this.currentUser.stats.historyQuizzes++;
        } else {
            this.currentUser.stats.spanishQuizzes++;
        }

        // Add to quiz history
        const quizResult = {
            date: new Date().toISOString(),
            subject: this.currentSubject,
            topic: this.currentTopic,
            score: this.score,
            total: this.questions.length,
            percentage: percentage
        };

        this.currentUser.stats.quizHistory.unshift(quizResult);

        // Keep only last 10 quiz results
        if (this.currentUser.stats.quizHistory.length > 10) {
            this.currentUser.stats.quizHistory = this.currentUser.stats.quizHistory.slice(0, 10);
        }

        // Update localStorage
        localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

        // Update users array
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userIndex = users.findIndex(u => u.username === this.currentUser.username);
        if (userIndex !== -1) {
            users[userIndex] = this.currentUser;
            localStorage.setItem('users', JSON.stringify(users));
        }
    }

    showProfile() {
        this.hideUserDropdown();
        this.updateProfileDisplay();
        this.showScreen('profile-screen');
    }

    updateProfileDisplay() {
        if (!this.currentUser) return;

        // Update header with username
        document.getElementById('profile-username-header').textContent = this.currentUser.username;

        // Stats
        const stats = this.currentUser.stats;
        document.getElementById('total-quizzes').textContent = stats.totalQuizzes;
        document.getElementById('best-score').textContent = `${stats.bestScore}%`;

        const averageScore = stats.totalQuestions > 0 ?
            Math.round((stats.totalCorrect / stats.totalQuestions) * 100) : 0;
        document.getElementById('average-score').textContent = `${averageScore}%`;

        // Subject progress
        const historyProgress = stats.historyQuizzes > 0 ? Math.min(stats.historyQuizzes * 10, 100) : 0;
        const spanishProgress = stats.spanishQuizzes > 0 ? Math.min(stats.spanishQuizzes * 10, 100) : 0;

        document.getElementById('history-progress').style.width = `${historyProgress}%`;
        document.getElementById('history-progress-text').textContent = `${historyProgress}%`;
        document.getElementById('spanish-progress').style.width = `${spanishProgress}%`;
        document.getElementById('spanish-progress-text').textContent = `${spanishProgress}%`;

        // Quiz history
        this.displayQuizHistory();
    }

    displayQuizHistory() {
        const container = document.getElementById('recent-quizzes');
        const history = this.currentUser.stats.quizHistory;

        if (history.length === 0) {
            container.innerHTML = '<p class="no-data">No recent activity</p>';
            return;
        }

        // Show only the last 3 quizzes for clean UI
        const recentHistory = history.slice(-3);

        container.innerHTML = recentHistory.map(quiz => {
            const date = new Date(quiz.date).toLocaleDateString();
            const subjectName = quiz.subject === 'history' ? 'Edexcel History' : 'AQA Spanish';

            return `
                <div class="quiz-history-item">
                    <div class="quiz-info">
                        <div class="quiz-subject">${subjectName}</div>
                        <div class="quiz-date">${date}</div>
                    </div>
                    <div class="quiz-score">${quiz.percentage}%</div>
                </div>
            `;
        }).join('');
    }

    getScoreClass(percentage) {
        if (percentage >= 90) return 'excellent';
        if (percentage >= 70) return 'good';
        if (percentage >= 50) return 'average';
        return 'poor';
    }

    getTopicDisplayName(topic) {
        const topicNames = {
            'keyTopic1': 'The Weimar Republic',
            'keyTopic2': 'Hitler\'s Rise to Power',
            'keyTopic3': 'Nazi Control and Dictatorship',
            'keyTopic4': 'Life in Nazi Germany',
            'all': 'All Edexcel History',
            'adjectives': 'Adjectives',
            'adverbs': 'Adverbs',
            'nouns': 'Nouns',
            'verbs': 'Verbs',
            'spanish-all': 'All AQA Spanish'
        };

        return topicNames[topic] || topic;
    }

    clearUserData() {
        if (confirm('Are you sure you want to clear all your quiz data? This cannot be undone.')) {
            this.currentUser.stats = {
                totalQuizzes: 0,
                totalQuestions: 0,
                totalCorrect: 0,
                historyQuizzes: 0,
                spanishQuizzes: 0,
                bestScore: 0,
                quizHistory: []
            };

            // Update localStorage
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

            // Update users array
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.username === this.currentUser.username);
            if (userIndex !== -1) {
                users[userIndex] = this.currentUser;
                localStorage.setItem('users', JSON.stringify(users));
            }

            this.updateProfileDisplay();
            this.showMessage('All quiz data cleared!', 'success');
        }
    }

    showSettings() {
        this.hideUserDropdown();
        this.updateSettingsDisplay();
        this.showScreen('settings-screen');
    }

    updateSettingsDisplay() {
        // Get current auto-skip setting
        let autoSkipEnabled = true; // Default value

        if (this.currentUser) {
            // Ensure user has settings object (for existing users)
            if (!this.currentUser.settings) {
                this.currentUser.settings = {
                    autoSkipEnabled: true
                };
            }
            autoSkipEnabled = this.currentUser.settings.autoSkipEnabled;
        } else {
            // For non-logged-in users, check localStorage for guest settings
            const guestSettings = localStorage.getItem('guestSettings');
            if (guestSettings) {
                const settings = JSON.parse(guestSettings);
                autoSkipEnabled = settings.autoSkipEnabled;
            }
        }

        // Update the toggle switch
        const autoSkipToggle = document.getElementById('auto-skip-toggle');
        autoSkipToggle.checked = autoSkipEnabled;
    }

    saveSettings() {
        // Get the toggle value
        const autoSkipToggle = document.getElementById('auto-skip-toggle');
        const autoSkipEnabled = autoSkipToggle.checked;

        if (this.currentUser) {
            // Save settings for logged-in user
            // Ensure user has settings object
            if (!this.currentUser.settings) {
                this.currentUser.settings = {};
            }

            this.currentUser.settings.autoSkipEnabled = autoSkipEnabled;

            // Update localStorage
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));

            // Update users array
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.username === this.currentUser.username);
            if (userIndex !== -1) {
                users[userIndex] = this.currentUser;
                localStorage.setItem('users', JSON.stringify(users));
            }
        } else {
            // Save settings for guest user
            const guestSettings = {
                autoSkipEnabled: autoSkipEnabled
            };
            localStorage.setItem('guestSettings', JSON.stringify(guestSettings));
        }

        this.showMessage('Settings saved successfully!', 'success');
        this.showSubjectScreen();
    }

    isAutoSkipEnabled() {
        if (this.currentUser && this.currentUser.settings) {
            return this.currentUser.settings.autoSkipEnabled;
        } else {
            // Check guest settings for non-logged-in users
            const guestSettings = localStorage.getItem('guestSettings');
            if (guestSettings) {
                const settings = JSON.parse(guestSettings);
                return settings.autoSkipEnabled;
            }
            return true; // Default to enabled
        }
    }

    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
    }

    resetQuiz() {
        this.currentTopic = null;
        this.questions = [];
        this.currentQuestionIndex = 0;
        this.score = 0;
        this.selectedAnswer = null;
        this.questionAnswered = false;
    }

    getExplanationDatabase() {
        return [
            {
                subtopic: "Origins of the Republic, 1918–19",
                keywords: ["kaiser", "wilhelm", "abdicated", "weimar", "constitution", "article 48"],
                explanation: "The Weimar Republic was established after Kaiser Wilhelm II abdicated in November 1918 following Germany's defeat in WWI. The new democratic constitution included Article 48, which allowed the president to rule by decree in emergencies - a provision that would later be exploited to undermine democracy."
            },
            {
                subtopic: "Early Challenges to the Weimar Republic, 1919–23",
                keywords: ["spartacist", "rosa luxemburg", "kapp putsch", "versailles", "hyperinflation", "ruhr"],
                explanation: "The early Weimar Republic faced severe challenges from both left and right. The Spartacist Uprising (1919) led by Rosa Luxemburg was crushed by the Freikorps, while the Kapp Putsch (1920) failed due to a general strike. The Treaty of Versailles created resentment, and hyperinflation in 1923 devastated the economy when France occupied the Ruhr."
            },
            {
                subtopic: "Golden Years: Recovery of the Republic, 1924–29",
                keywords: ["stresemann", "rentenmark", "dawes plan", "young plan", "locarno", "league of nations"],
                explanation: "Gustav Stresemann was key to Germany's recovery. He introduced the Rentenmark in 1923 to end hyperinflation, negotiated the Dawes Plan (1924) and Young Plan (1929) to restructure reparations, and improved international relations through the Locarno Pact (1925) and Germany's entry into the League of Nations (1926)."
            },
            {
                subtopic: "Changes in Society, 1924–29",
                keywords: ["women", "culture", "bauhaus", "cinema", "standard of living"],
                explanation: "The 'Golden Years' saw improvements in living standards, women's rights (including voting), and a flourishing of culture. The Bauhaus movement revolutionized architecture and design, while German cinema produced masterpieces like 'Metropolis' by Fritz Lang."
            },
            {
                subtopic: "Early Development of the Nazi Party, 1920–22",
                keywords: ["hitler", "german workers party", "nazi party", "twenty-five point", "sa", "stormtroopers"],
                explanation: "Hitler joined the German Workers' Party in 1919 and quickly became its leader, renaming it the Nazi Party. The Twenty-Five Point Programme outlined anti-Semitic and nationalist policies. The SA (Stormtroopers) were used for propaganda, intimidation, and protecting Nazi rallies."
            },
            {
                subtopic: "Munich Putsch and Nazi Party, 1923–28",
                keywords: ["munich putsch", "mein kampf", "prison", "bamberg conference"],
                explanation: "The Munich Putsch (1923) was Hitler's failed attempt to seize power in Bavaria. Though it failed and led to his arrest, it gave him national publicity. While in prison, he wrote 'Mein Kampf' outlining his racist ideology. The Bamberg Conference (1926) consolidated his control over the Nazi Party."
            },
            {
                subtopic: "Growth in Support for the Nazis, 1929–32",
                keywords: ["wall street crash", "unemployment", "communist", "propaganda", "charisma"],
                explanation: "The Wall Street Crash (1929) caused massive unemployment in Germany, which boosted support for extremist parties. The growth of the Communist Party scared the middle classes toward the Nazis. Hitler's charisma, effective propaganda, and the SA's street presence helped increase Nazi support dramatically."
            },
            {
                subtopic: "How Hitler Became Chancellor, 1932–33",
                keywords: ["chancellor", "hindenburg", "von papen", "january 1933", "largest party"],
                explanation: "In 1932 elections, the Nazis became the largest party but lacked a majority. President Hindenburg initially refused to appoint Hitler as Chancellor. However, political maneuvering by von Papen led to Hitler's appointment as Chancellor in January 1933, with the mistaken belief they could control him."
            },
            {
                subtopic: "Creation of a Dictatorship, 1933–34",
                keywords: ["reichstag fire", "enabling act", "night of long knives", "hindenburg death", "führer"],
                explanation: "Hitler rapidly consolidated power through key events: the Reichstag Fire (February 1933) was blamed on communists and led to emergency powers; the Enabling Act (March 1933) allowed Hitler to pass laws without parliament; the Night of the Long Knives (June 1934) eliminated SA leadership; and Hindenburg's death (August 1934) allowed Hitler to become Führer."
            },
            {
                subtopic: "The Police State",
                keywords: ["gestapo", "ss", "concentration camps", "people's court"],
                explanation: "The Nazi police state used multiple organizations to control the population: the Gestapo (secret police) monitored and arrested opponents; the SS ran concentration camps for political prisoners; and the legal system was corrupted with judges swearing loyalty to Hitler and the People's Court handling political trials."
            },
            {
                subtopic: "Controlling and Influencing Attitudes",
                keywords: ["goebbels", "propaganda", "nuremberg rallies", "olympics", "degenerate art"],
                explanation: "Joseph Goebbels' Propaganda Ministry controlled all media and organized spectacular events like the Nuremberg Rallies and the 1936 Berlin Olympics to showcase Nazi power. The regime promoted Nazi-approved art and architecture while banning 'degenerate' art that didn't fit their ideology."
            },
            {
                subtopic: "Opposition, Resistance, and Conformity",
                keywords: ["niemöller", "confessing church", "swing youth", "edelweiss pirates"],
                explanation: "While most Germans conformed due to fear, propaganda, and economic benefits, some resisted. Pastor Niemöller led the Confessing Church against Nazi religious policies. Youth groups like the Swing Youth and Edelweiss Pirates rejected Nazi ideology, but opposition had limited impact due to severe repression."
            },
            {
                subtopic: "Nazi Policies Towards Women",
                keywords: ["homemakers", "motherhood", "mother's cross", "traditional"],
                explanation: "Nazi ideology promoted women as homemakers focused on 'Kinder, Küche, Kirche' (children, kitchen, church). Policies included the Mother's Cross for large families, restrictions on women's employment, and promotion of traditional appearance. This reversed many advances women had made during the Weimar period."
            },
            {
                subtopic: "Nazi Policies Towards the Young",
                keywords: ["hitler youth", "league of german maidens", "education", "indoctrination"],
                explanation: "The Nazis targeted youth through the Hitler Youth (boys) and League of German Maidens (girls), which indoctrinated loyalty and promoted physical fitness. Education was transformed with Nazi curriculum, anti-Semitic teachings, and teachers required to be loyal to the regime."
            },
            {
                subtopic: "Employment and Living Standards",
                keywords: ["unemployment", "autobahns", "rearmament", "strength through joy", "labour front"],
                explanation: "The Nazis reduced unemployment through public works (like autobahn construction), rearmament, and the National Labour Service. However, 'invisible unemployment' excluded Jews and women from statistics. Programs like 'Strength Through Joy' provided holidays, but wages remained stagnant and trade unions were replaced by the Labour Front."
            },
            {
                subtopic: "Persecution of Minorities",
                keywords: ["racial ideology", "jewish", "nuremberg laws", "kristallnacht", "sterilization"],
                explanation: "Nazi racial ideology targeted multiple groups: Slavs, Roma, homosexuals, and disabled people faced persecution including forced sterilization. Jewish persecution escalated from the 1933 shop boycotts, through the Nuremberg Laws (1935) that stripped citizenship, to Kristallnacht (1938) when synagogues and businesses were attacked."
            }
        ];
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
}

// Initialize the quiz app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new QuizApp();
});
