/* Dark Mode Theme */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --accent-primary: #4a9eff;
    --accent-hover: #3a8eef;
    --success: #4caf50;
    --error: #f44336;
    --warning: #ff9800;
    --border: #444444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    scroll-behavior: smooth; /* Smooth scrolling for better UX */
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px 20px 80px 20px; /* Extra bottom padding for clean scrolling */
    min-height: 100vh;
}

header {
    margin-bottom: 40px;
    padding: 20px 0;
    border-bottom: 2px solid var(--border);
}

.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

header h1 {
    font-size: 2.5rem;
    color: var(--accent-primary);
    margin: 0;
    text-align: center;
}

/* User Account Section */
.user-section {
    display: flex;
    align-items: center;
    gap: 15px;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.user-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bg-secondary);
    border: 2px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
}

.user-icon:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
    color: var(--accent-primary);
    transform: scale(1.05);
}

.user-icon svg {
    width: 20px;
    height: 20px;
}

/* User Dropdown Menu */
.user-dropdown {
    position: absolute;
    top: 50px;
    right: 0;
    background: var(--bg-secondary);
    border: 2px solid var(--border);
    border-radius: 8px;
    min-width: 180px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-header {
    padding: 12px 16px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.dropdown-divider {
    height: 1px;
    background: var(--border);
    margin: 0 8px;
}

.dropdown-item {
    width: 100%;
    padding: 12px 16px;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background: var(--bg-tertiary);
}

.dropdown-item:last-child {
    border-radius: 0 0 6px 6px;
}

.dropdown-item svg {
    width: 16px;
    height: 16px;
    color: var(--text-secondary);
}

.hidden {
    display: none !important;
}

/* Subject Tabs */
.subject-tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.tab-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border);
    border-radius: 8px;
    padding: 12px 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
}

.tab-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
}

.tab-btn.active {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: white;
}

/* Screen Management */
.screen {
    display: none;
    padding-bottom: 60px; /* Ensure content is never cut off at bottom */
    min-height: calc(100vh - 120px); /* Account for header height */
}

.screen.active {
    display: block;
}

/* Screen Titles */
.screen h2 {
    text-align: center;
    color: var(--accent-primary);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    margin-top: 20px; /* Space from header */
}

/* Topic Selection */
.topic-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
    margin-bottom: 40px; /* Extra space at bottom for clean scrolling */
}

.topic-btn {
    background: var(--bg-secondary);
    border: 2px solid var(--border);
    border-radius: 12px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    color: var(--text-primary);
}

.topic-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

.topic-btn h3 {
    font-size: 1.3rem;
    margin-bottom: 8px;
    color: var(--accent-primary);
}

.topic-btn p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Quiz Screen */
.quiz-header {
    margin-bottom: 30px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--accent-primary);
    width: 0%;
    transition: width 0.3s ease;
}

/* Removed conflicting quiz-info rule - using profile-specific styling below */

.question-container {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 20px;
}

#question-text {
    font-size: 1.4rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.question-type {
    background: var(--accent-primary);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 25px;
    font-weight: 500;
}

.answers-container {
    margin-bottom: 30px;
}

.answer-option {
    background: var(--bg-tertiary);
    border: 2px solid var(--border);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: block;
    width: 100%;
    text-align: left;
    color: var(--text-primary);
    font-size: 1rem;
}

.answer-option:hover {
    background: var(--bg-primary);
    border-color: var(--accent-primary);
}

.answer-option.selected {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: white;
}

.answer-option.correct {
    background: var(--success);
    border-color: var(--success);
    color: white;
}

.answer-option.incorrect {
    background: var(--error);
    border-color: var(--error);
    color: white;
}

/* Explanation Box */
.explanation {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    animation: slideIn 0.3s ease-out;
}

.explanation.correct-explanation {
    border: 2px solid var(--success);
}

.explanation.incorrect-explanation {
    border: 2px solid var(--warning);
}

.explanation-header {
    font-size: 1.1rem;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.correct-explanation .explanation-header {
    color: var(--success);
}

.incorrect-explanation .explanation-header {
    color: var(--warning);
}

.explanation-content {
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 0.95rem;
}

.explanation-content strong {
    color: var(--accent-primary);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.quiz-controls {
    display: flex;
    gap: 15px;
    justify-content: space-between;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--accent-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
}

.btn-primary:disabled {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 2px solid var(--border);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    border-color: var(--accent-primary);
}

/* Results Screen */
.results-container {
    text-align: center;
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 40px;
}

.results-container h2 {
    font-size: 2rem;
    margin-bottom: 30px;
    color: var(--accent-primary);
}

.final-score {
    margin-bottom: 30px;
}

#final-score-text {
    font-size: 1.5rem;
    display: block;
    margin-bottom: 15px;
}

.score-percentage {
    font-size: 3rem;
    font-weight: bold;
    color: var(--accent-primary);
}

.performance-message {
    font-size: 1.1rem;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-tertiary);
    border-radius: 8px;
    color: var(--text-secondary);
}

.results-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 15px 15px 60px 15px; /* Extra bottom padding on mobile */
    }

    .header-content {
        padding: 0 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .user-info {
        flex-direction: column;
        gap: 10px;
    }

    .topic-grid {
        grid-template-columns: 1fr;
    }

    .quiz-controls {
        flex-direction: column;
    }

    .results-actions {
        flex-direction: column;
    }

    .question-container {
        padding: 20px;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    background-color: var(--bg-secondary);
    margin: 5% auto;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    position: relative;
}

.close {
    color: var(--text-secondary);
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 15px;
}

.close:hover {
    color: var(--text-primary);
}

.auth-form h2 {
    color: var(--accent-primary);
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--border);
    border-radius: 6px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.auth-switch {
    text-align: center;
    margin-top: 15px;
    color: var(--text-secondary);
}

.auth-switch a {
    color: var(--accent-primary);
    text-decoration: none;
}

.auth-switch a:hover {
    text-decoration: underline;
}

/* Profile Screen - Clean Design */
.profile-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 0 20px;
}

.profile-container h2 {
    color: var(--text-primary);
    margin-bottom: 40px;
    text-align: center;
    font-size: 1.8rem;
    font-weight: 600;
}

/* Stats Cards */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    border: 1px solid var(--border);
    transition: all 0.2s ease;
}

.stat-card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Subject Cards */
.subject-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.subject-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--border);
    transition: all 0.2s ease;
}

.subject-card:hover {
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

.subject-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.subject-header h3 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.subject-score {
    color: var(--accent-primary);
    font-weight: 600;
    font-size: 1rem;
}

.progress-bar {
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

/* Recent Activity */
.recent-activity {
    margin-bottom: 40px;
}

.recent-activity h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 16px;
}

.activity-list {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border);
    min-height: 80px;
}

.quiz-history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border);
}

.quiz-history-item:last-child {
    border-bottom: none;
}

/* Profile Recent Activity Styling */
.quiz-info {
    flex: 1;
}

.quiz-subject {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.2;
}

.quiz-date {
    color: var(--text-secondary);
    font-size: 0.85rem;
    line-height: 1.2;
}

.quiz-score {
    color: var(--accent-primary);
    font-weight: 600;
    font-size: 1rem;
    flex-shrink: 0;
    margin-left: 12px;
}

.no-data {
    color: var(--text-secondary);
    text-align: center;
    font-size: 0.9rem;
}

/* Profile Actions */
.profile-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 20px;
}

.btn-warning {
    background: var(--warning);
    color: white;
    border: 2px solid var(--warning);
}

.btn-warning:hover {
    background: #e68900;
    border-color: #e68900;
}

/* Settings Screen */
.settings-container {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 30px;
    max-width: 600px;
    margin: 0 auto;
}

.settings-container h2 {
    color: var(--accent-primary);
    margin-bottom: 30px;
    text-align: center;
}

.settings-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-tertiary);
    border-radius: 8px;
}

.settings-section h3 {
    color: var(--accent-primary);
    margin-bottom: 20px;
    border-bottom: 2px solid var(--border);
    padding-bottom: 8px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
    margin-right: 20px;
}

.setting-label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1rem;
    display: block;
    margin-bottom: 5px;
}

.setting-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

.setting-control {
    flex-shrink: 0;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-primary);
    border: 2px solid var(--border);
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: var(--text-secondary);
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
    background-color: white;
}

.toggle-slider:hover {
    border-color: var(--accent-primary);
}

.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* Responsive adjustments for profile and settings */
@media (max-width: 600px) {
    .profile-stats {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .subject-cards {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .profile-actions {
        flex-direction: column;
        gap: 12px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .setting-info {
        margin-right: 0;
    }

    .settings-actions {
        flex-direction: column;
    }
}

/* Notification Messages */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.95rem;
    z-index: 1001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border: 2px solid;
    animation: notificationSlide 0.3s ease-out;
    max-width: 350px;
    word-wrap: break-word;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification.success {
    background: var(--bg-secondary);
    color: var(--success);
    border-color: var(--success);
}

.notification.error {
    background: var(--bg-secondary);
    color: var(--error);
    border-color: var(--error);
}

.notification.warning {
    background: var(--bg-secondary);
    color: var(--warning);
    border-color: var(--warning);
}

@keyframes notificationSlide {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification.fade-out {
    animation: notificationFadeOut 0.3s ease-in forwards;
}

@keyframes notificationFadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Responsive notifications */
@media (max-width: 600px) {
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
