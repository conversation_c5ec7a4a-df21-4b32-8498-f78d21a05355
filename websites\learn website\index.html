<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1>Quiz</h1>

                <!-- User Account Section -->
                <div class="user-section">
                    <div id="user-icon" class="user-icon" title="Account">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                    </div>

                    <!-- User Dropdown Menu -->
                    <div id="user-dropdown" class="user-dropdown hidden">
                        <div class="dropdown-header">
                            <span id="dropdown-username">User</span>
                        </div>
                        <div class="dropdown-divider"></div>
                        <button id="profile-btn" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            Profile
                        </button>
                        <button id="settings-btn" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                            </svg>
                            Settings
                        </button>
                        <button id="logout-btn" class="dropdown-item">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                            </svg>
                            Logout
                        </button>
                    </div>
                </div>
            </div>

            <!-- Subject Tabs -->
            <div class="subject-tabs">
                <button class="tab-btn active" data-subject="history">Edexcel History</button>
                <button class="tab-btn" data-subject="spanish">AQA Spanish</button>
            </div>
        </header>

        <!-- History Topic Selection Screen -->
        <div id="history-topic-selection" class="screen active">
            <h2>Select an Edexcel History Topic</h2>
            <div class="topic-grid">
                <button class="topic-btn" data-topic="keyTopic1">
                    <h3>The Weimar Republic</h3>
                    <p>1918–29</p>
                </button>
                <button class="topic-btn" data-topic="keyTopic2">
                    <h3>Hitler's Rise to Power</h3>
                    <p>1919–33</p>
                </button>
                <button class="topic-btn" data-topic="keyTopic3">
                    <h3>Nazi Control and Dictatorship</h3>
                    <p>1933–39</p>
                </button>
                <button class="topic-btn" data-topic="keyTopic4">
                    <h3>Life in Nazi Germany</h3>
                    <p>1933–39</p>
                </button>
                <button class="topic-btn" data-topic="all">
                    <h3>All Topics</h3>
                    <p>Mixed Questions</p>
                </button>
            </div>
        </div>

        <!-- Spanish Topic Selection Screen -->
        <div id="spanish-topic-selection" class="screen">
            <h2>Select AQA Spanish Category</h2>
            <div class="topic-grid">
                <button class="topic-btn" data-topic="adjectives">
                    <h3>Adjectives</h3>
                    <p>Descriptive words</p>
                </button>
                <button class="topic-btn" data-topic="adverbs">
                    <h3>Adverbs</h3>
                    <p>Manner, time, place</p>
                </button>
                <button class="topic-btn" data-topic="nouns">
                    <h3>Nouns</h3>
                    <p>People, places, things</p>
                </button>
                <button class="topic-btn" data-topic="verbs">
                    <h3>Verbs</h3>
                    <p>Actions and states</p>
                </button>
                <button class="topic-btn" data-topic="spanish-all">
                    <h3>All Vocabulary</h3>
                    <p>Mixed Spanish words</p>
                </button>
            </div>
        </div>

        <!-- Quiz Screen -->
        <div id="quiz-screen" class="screen">
            <div class="quiz-header">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <div class="quiz-info">
                    <span id="question-counter">Question 1 of 10</span>
                    <span id="score">Score: 0/0</span>
                </div>
            </div>

            <div class="question-container">
                <h2 id="question-text">Question will appear here</h2>
                <div id="question-type" class="question-type"></div>
                
                <div id="answers-container" class="answers-container">
                    <!-- Answers will be dynamically generated -->
                </div>

                <div class="quiz-controls">
                    <button id="next-btn" class="btn btn-primary" disabled>Next Question</button>
                    <button id="back-to-topics" class="btn btn-secondary">Back to Topics</button>
                </div>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="screen">
            <div class="results-container">
                <h2>Quiz Complete!</h2>
                <div class="final-score">
                    <span id="final-score-text">Your Score: 0/10</span>
                    <div class="score-percentage" id="score-percentage">0%</div>
                </div>
                
                <div class="performance-message" id="performance-message">
                    <!-- Performance message will be generated -->
                </div>

                <div class="results-actions">
                    <button id="retake-quiz" class="btn btn-primary">Retake Quiz</button>
                    <button id="new-topic" class="btn btn-secondary">Choose New Topic</button>
                </div>
            </div>
        </div>

        <!-- Login/Register Modal -->
        <div id="auth-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>

                <!-- Login Form -->
                <div id="login-form" class="auth-form">
                    <h2>Login</h2>
                    <form id="login-form-element">
                        <div class="form-group">
                            <label for="login-username">Username:</label>
                            <input type="text" id="login-username" required>
                        </div>
                        <div class="form-group">
                            <label for="login-password">Password:</label>
                            <input type="password" id="login-password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Login</button>
                        <p class="auth-switch">Don't have an account? <a href="#" id="show-register">Register here</a></p>
                    </form>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="auth-form hidden">
                    <h2>Register</h2>
                    <form id="register-form-element">
                        <div class="form-group">
                            <label for="register-username">Username:</label>
                            <input type="text" id="register-username" required>
                        </div>
                        <div class="form-group">
                            <label for="register-email">Email:</label>
                            <input type="email" id="register-email" required>
                        </div>
                        <div class="form-group">
                            <label for="register-password">Password:</label>
                            <input type="password" id="register-password" required>
                        </div>
                        <div class="form-group">
                            <label for="register-confirm-password">Confirm Password:</label>
                            <input type="password" id="register-confirm-password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Register</button>
                        <p class="auth-switch">Already have an account? <a href="#" id="show-login">Login here</a></p>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Screen -->
        <div id="profile-screen" class="screen">
            <div class="profile-container">
                <h2 id="profile-username-header">User Profile</h2>

                <!-- Key Stats Cards -->
                <div class="profile-stats">
                    <div class="stat-card">
                        <div class="stat-value" id="total-quizzes">0</div>
                        <div class="stat-label">Quizzes Taken</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="average-score">0%</div>
                        <div class="stat-label">Average Score</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="best-score">0%</div>
                        <div class="stat-label">Best Score</div>
                    </div>
                </div>

                <!-- Subject Progress -->
                <div class="subject-cards">
                    <div class="subject-card">
                        <div class="subject-header">
                            <h3>Edexcel History</h3>
                            <span class="subject-score" id="history-progress-text">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="history-progress"></div>
                        </div>
                    </div>
                    <div class="subject-card">
                        <div class="subject-header">
                            <h3>AQA Spanish</h3>
                            <span class="subject-score" id="spanish-progress-text">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="spanish-progress"></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity (simplified) -->
                <div class="recent-activity">
                    <h3>Recent Activity</h3>
                    <div id="recent-quizzes" class="activity-list">
                        <p class="no-data">No recent activity</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="profile-actions">
                    <button id="back-from-profile" class="btn btn-secondary">Back</button>
                    <button id="clear-data" class="btn btn-warning">Clear Data</button>
                </div>
            </div>
        </div>

        <!-- Settings Screen -->
        <div id="settings-screen" class="screen">
            <div class="settings-container">
                <h2>Settings</h2>

                <div class="settings-section">
                    <h3>Quiz Behavior</h3>

                    <div class="setting-item">
                        <div class="setting-info">
                            <label for="auto-skip-toggle" class="setting-label">Auto-skip to next question</label>
                            <p class="setting-description">Automatically advance to the next question after 4 seconds when you answer correctly</p>
                        </div>
                        <div class="setting-control">
                            <label class="toggle-switch">
                                <input type="checkbox" id="auto-skip-toggle" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="back-from-settings" class="btn btn-secondary">Back</button>
                    <button id="save-settings" class="btn btn-primary">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <script src="content.js"></script>
    <script src="spanish_content.js"></script>
    <script src="script.js"></script>
</body>
</html>
